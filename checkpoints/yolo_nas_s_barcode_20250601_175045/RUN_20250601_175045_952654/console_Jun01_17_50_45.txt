============================================================
New run started at 2025-06-01.17:50:37.391057
sys.argv: "train_barcode_final.py"
============================================================
The console stream is logged into /Users/<USER>/sg_logs/console.log
/Volumes/DATA/projects/Yolo-nas/src/super_gradients/common/environment/cfg_utils.py:6: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/albumentations/__init__.py:24: UserWarning: A new version of Albumentations is available: 2.0.8 (you have 1.4.24). Upgrade using: pip install -U albumentations. To disable automatic update checks, set the environment variable NO_ALBUMENTATIONS_UPDATE to 1.
  check_for_updates()
🚀 YOLO-NAS Barcode Detection Training
🍎 Optimized for Apple Silicon Mac Mini M4
============================================================
Start time: 2025-06-01 17:50:39

🍎 Configuring Apple Silicon optimizations...
✅ MPS cache cleared
✅ Apple Silicon optimizations applied
✅ MPS available and functional: mps

🔧 Initializing SuperGradients...
✅ SuperGradients initialized
📊 Creating dataloaders...

Indexing dataset annotations:   0%|                                                                                                                                                                                          | 0/28696 [00:00<?, ?it/s]
Indexing dataset annotations:   2%|██▊                                                                                                                                                                           | 471/28696 [00:00<00:05, 4706.14it/s]
Indexing dataset annotations:   4%|██████                                                                                                                                                                       | 1005/28696 [00:00<00:05, 5073.48it/s]
Indexing dataset annotations:   5%|█████████▎                                                                                                                                                                   | 1539/28696 [00:00<00:05, 5194.14it/s]
Indexing dataset annotations:   7%|████████████▍                                                                                                                                                                | 2070/28696 [00:00<00:05, 5239.75it/s]
Indexing dataset annotations:   9%|███████████████▋                                                                                                                                                             | 2605/28696 [00:00<00:04, 5277.49it/s]
Indexing dataset annotations:  11%|██████████████████▉                                                                                                                                                          | 3133/28696 [00:00<00:04, 5278.09it/s]
Indexing dataset annotations:  13%|██████████████████████                                                                                                                                                       | 3668/28696 [00:00<00:04, 5299.81it/s]
Indexing dataset annotations:  15%|█████████████████████████▎                                                                                                                                                   | 4200/28696 [00:00<00:04, 5303.28it/s]
Indexing dataset annotations:  16%|████████████████████████████▌                                                                                                                                                | 4731/28696 [00:00<00:04, 5301.68it/s]
Indexing dataset annotations:  18%|███████████████████████████████▋                                                                                                                                             | 5266/28696 [00:01<00:04, 5314.03it/s]
Indexing dataset annotations:  20%|██████████████████████████████████▉                                                                                                                                          | 5798/28696 [00:01<00:04, 5241.24it/s]
Indexing dataset annotations:  22%|██████████████████████████████████████▏                                                                                                                                      | 6332/28696 [00:01<00:04, 5268.03it/s]
Indexing dataset annotations:  24%|█████████████████████████████████████████▍                                                                                                                                   | 6869/28696 [00:01<00:04, 5298.61it/s]
Indexing dataset annotations:  26%|████████████████████████████████████████████▌                                                                                                                                | 7402/28696 [00:01<00:04, 5306.62it/s]
Indexing dataset annotations:  28%|███████████████████████████████████████████████▊                                                                                                                             | 7936/28696 [00:01<00:03, 5314.29it/s]
Indexing dataset annotations:  30%|███████████████████████████████████████████████████                                                                                                                          | 8468/28696 [00:01<00:03, 5299.06it/s]
Indexing dataset annotations:  31%|██████████████████████████████████████████████████████▏                                                                                                                      | 8998/28696 [00:01<00:03, 5296.47it/s]
Indexing dataset annotations:  33%|█████████████████████████████████████████████████████████▍                                                                                                                   | 9530/28696 [00:01<00:03, 5301.12it/s]
Indexing dataset annotations:  35%|████████████████████████████████████████████████████████████▎                                                                                                               | 10065/28696 [00:01<00:03, 5314.79it/s]
Indexing dataset annotations:  37%|███████████████████████████████████████████████████████████████▌                                                                                                            | 10599/28696 [00:02<00:03, 5320.45it/s]
Indexing dataset annotations:  39%|██████████████████████████████████████████████████████████████████▋                                                                                                         | 11132/28696 [00:02<00:03, 5321.52it/s]
Indexing dataset annotations:  41%|█████████████████████████████████████████████████████████████████████▉                                                                                                      | 11668/28696 [00:02<00:03, 5332.38it/s]
Indexing dataset annotations:  43%|█████████████████████████████████████████████████████████████████████████▏                                                                                                  | 12202/28696 [00:02<00:03, 5333.07it/s]
Indexing dataset annotations:  44%|████████████████████████████████████████████████████████████████████████████▎                                                                                               | 12740/28696 [00:02<00:02, 5346.94it/s]
Indexing dataset annotations:  46%|███████████████████████████████████████████████████████████████████████████████▌                                                                                            | 13275/28696 [00:02<00:02, 5338.29it/s]
Indexing dataset annotations:  48%|██████████████████████████████████████████████████████████████████████████████████▊                                                                                         | 13809/28696 [00:02<00:02, 5324.31it/s]
Indexing dataset annotations:  50%|█████████████████████████████████████████████████████████████████████████████████████▉                                                                                      | 14342/28696 [00:02<00:02, 5322.16it/s]
Indexing dataset annotations:  52%|█████████████████████████████████████████████████████████████████████████████████████████▏                                                                                  | 14875/28696 [00:02<00:02, 5307.80it/s]
Indexing dataset annotations:  54%|████████████████████████████████████████████████████████████████████████████████████████████▎                                                                               | 15407/28696 [00:02<00:02, 5310.65it/s]
Indexing dataset annotations:  56%|███████████████████████████████████████████████████████████████████████████████████████████████▌                                                                            | 15942/28696 [00:03<00:02, 5319.52it/s]
Indexing dataset annotations:  57%|██████████████████████████████████████████████████████████████████████████████████████████████████▋                                                                         | 16474/28696 [00:03<00:02, 5248.38it/s]
Indexing dataset annotations:  59%|█████████████████████████████████████████████████████████████████████████████████████████████████████▉                                                                      | 17007/28696 [00:03<00:02, 5272.26it/s]
Indexing dataset annotations:  61%|█████████████████████████████████████████████████████████████████████████████████████████████████████████                                                                   | 17537/28696 [00:03<00:02, 5278.41it/s]
Indexing dataset annotations:  63%|████████████████████████████████████████████████████████████████████████████████████████████████████████████▎                                                               | 18065/28696 [00:03<00:02, 5272.38it/s]
Indexing dataset annotations:  65%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████▍                                                            | 18598/28696 [00:03<00:01, 5289.37it/s]
Indexing dataset annotations:  67%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████▋                                                         | 19132/28696 [00:03<00:01, 5303.32it/s]
Indexing dataset annotations:  69%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▊                                                      | 19664/28696 [00:03<00:01, 5305.79it/s]
Indexing dataset annotations:  70%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████                                                   | 20201/28696 [00:03<00:01, 5322.19it/s]
Indexing dataset annotations:  72%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▎                                               | 20734/28696 [00:03<00:01, 5321.50it/s]
Indexing dataset annotations:  74%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▍                                            | 21267/28696 [00:04<00:01, 5320.95it/s]
Indexing dataset annotations:  76%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▋                                         | 21800/28696 [00:04<00:01, 5321.02it/s]
Indexing dataset annotations:  78%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▊                                      | 22333/28696 [00:04<00:01, 5312.90it/s]
Indexing dataset annotations:  80%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████                                   | 22867/28696 [00:04<00:01, 5318.69it/s]
Indexing dataset annotations:  82%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▎                               | 23400/28696 [00:04<00:00, 5321.85it/s]
Indexing dataset annotations:  83%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▍                            | 23937/28696 [00:04<00:00, 5335.62it/s]
Indexing dataset annotations:  85%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▋                         | 24474/28696 [00:04<00:00, 5345.49it/s]
Indexing dataset annotations:  87%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▉                      | 25009/28696 [00:04<00:00, 5343.36it/s]
Indexing dataset annotations:  89%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████                   | 25544/28696 [00:04<00:00, 5331.41it/s]
Indexing dataset annotations:  91%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▎               | 26078/28696 [00:04<00:00, 5328.37it/s]
Indexing dataset annotations:  93%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▌            | 26611/28696 [00:05<00:00, 5313.05it/s]
Indexing dataset annotations:  95%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▋         | 27145/28696 [00:05<00:00, 5318.09it/s]
Indexing dataset annotations:  96%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▉      | 27680/28696 [00:05<00:00, 5324.64it/s]
Indexing dataset annotations:  98%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████   | 28213/28696 [00:05<00:00, 5324.68it/s]
Indexing dataset annotations: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 28696/28696 [00:05<00:00, 5302.77it/s]

Indexing dataset annotations:   0%|                                                                                                                                                                                           | 0/2382 [00:00<?, ?it/s]
Indexing dataset annotations:  23%|████████████████████████████████████████                                                                                                                                       | 545/2382 [00:00<00:00, 5442.60it/s]
Indexing dataset annotations:  49%|█████████████████████████████████████████████████████████████████████████████████████▏                                                                                        | 1166/2382 [00:00<00:00, 5888.84it/s]
Indexing dataset annotations:  77%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▌                                        | 1828/2382 [00:00<00:00, 6220.84it/s]
Indexing dataset annotations: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2382/2382 [00:00<00:00, 6225.81it/s]
✅ Training batches: 1793
✅ Validation batches: 149
🔄 Loading YOLO-NAS-S with COCO transfer learning...
Loading COCO pretrained weights...
Creating barcode detection model...
Transferring compatible weights...
✅ Transferred 915 layers from COCO pretrained model
✅ Model moved to: mps:0
⚙️ Creating training parameters...
✅ Training parameters configured
✅ Trainer created: yolo_nas_s_barcode_20250601_175045

📋 Training Summary:
🏗️ Model: YOLO-NAS-S (915 layers from COCO)
🎯 Classes: 2 (Barcode, QR Code)
📊 Dataset: 28,696 train + 2,382 val images
📱 Device: mps
⏱️ Epochs: 100
📦 Batch size: 16
🧠 Mixed precision: True

🏃 Starting training...
The console stream is now moved to checkpoints/yolo_nas_s_barcode_20250601_175045/RUN_20250601_175045_952654/console_Jun01_17_50_45.txt
[2025-06-01 17:50:45] INFO - sg_trainer.py - Using EMA with params {'decay': 0.9999}
[2025-06-01 17:50:45] WARNING - ema.py - Parameter decay_type is not specified for EMA model. Please specify decay_type parameter explicitly in your config:
ema: True
ema_params: 
  decay: 0.9999
  decay_type: constant|exp|threshold
Will default to `exp` decay with beta = 15
In the next major release of SG this warning will become an error.
/Volumes/DATA/projects/Yolo-nas/src/super_gradients/training/sg_trainer/sg_trainer.py:1761: UserWarning: Mixed precision training is not supported on CPU. Disabling mixed precision. (i.e. `mixed_precision=False`)
  warnings.warn("Mixed precision training is not supported on CPU. Disabling mixed precision. (i.e. `mixed_precision=False`)")
/Volumes/DATA/projects/Yolo-nas/src/super_gradients/training/sg_trainer/sg_trainer.py:1765: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  self.scaler = GradScaler(enabled=mixed_precision_enabled)
[2025-06-01 17:51:10] INFO - sg_trainer_utils.py - TRAINING PARAMETERS:
    - Mode:                         Single GPU
    - Number of GPUs:               0          (0 available on the machine)
    - Full dataset size:            28696      (len(train_set))
    - Batch size per GPU:           16         (batch_size)
    - Batch Accumulate:             1          (batch_accumulate)
    - Total batch size:             16         (num_gpus * batch_size)
    - Effective Batch size:         16         (num_gpus * batch_size * batch_accumulate)
    - Iterations per epoch:         1793       (len(train_loader))
    - Gradient updates per epoch:   1793       (len(train_loader) / batch_accumulate)
    - Model: YoloNAS_S  (19.02M parameters, 19.02M optimized)
    - Learning Rates and Weight Decays:
      - default: (19.02M parameters). LR: 0.0001 (19.02M parameters) WD: 0.0, (42.13K parameters), WD: 0.0005, (18.98M parameters)

[2025-06-01 17:51:10] INFO - sg_trainer.py - Started training for 100 epochs (0/99)


  0%|          | 0/1793 [00:00<?, ?it/s]
Train epoch 0:   0%|          | 0/1793 [00:00<?, ?it/s]/Volumes/DATA/projects/Yolo-nas/src/super_gradients/training/sg_trainer/sg_trainer.py:503: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast(enabled=self.training_params.mixed_precision):

Train epoch 0:   0%|          | 0/1793 [00:13<?, ?it/s, PPYoloELoss/loss=35.5, PPYoloELoss/loss_cls=33.2, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   0%|          | 1/1793 [00:13<6:49:26, 13.71s/it, PPYoloELoss/loss=35.5, PPYoloELoss/loss_cls=33.2, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   0%|          | 1/1793 [00:26<6:49:26, 13.71s/it, PPYoloELoss/loss=26.7, PPYoloELoss/loss_cls=24.4, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   0%|          | 2/1793 [00:26<6:24:23, 12.88s/it, PPYoloELoss/loss=26.7, PPYoloELoss/loss_cls=24.4, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   0%|          | 2/1793 [00:38<6:24:23, 12.88s/it, PPYoloELoss/loss=23, PPYoloELoss/loss_cls=20.7, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.16, gpu_mem=0]  
Train epoch 0:   0%|          | 3/1793 [00:38<6:17:22, 12.65s/it, PPYoloELoss/loss=23, PPYoloELoss/loss_cls=20.7, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.16, gpu_mem=0]
Train epoch 0:   0%|          | 3/1793 [00:50<6:17:22, 12.65s/it, PPYoloELoss/loss=20, PPYoloELoss/loss_cls=17.7, PPYoloELoss/loss_dfl=1.15, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   0%|          | 4/1793 [00:50<6:15:28, 12.59s/it, PPYoloELoss/loss=20, PPYoloELoss/loss_cls=17.7, PPYoloELoss/loss_dfl=1.15, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   0%|          | 4/1793 [01:03<6:15:28, 12.59s/it, PPYoloELoss/loss=17.7, PPYoloELoss/loss_cls=15.3, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.18, gpu_mem=0]
Train epoch 0:   0%|          | 5/1793 [01:03<6:13:26, 12.53s/it, PPYoloELoss/loss=17.7, PPYoloELoss/loss_cls=15.3, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.18, gpu_mem=0]
Train epoch 0:   0%|          | 5/1793 [01:16<6:13:26, 12.53s/it, PPYoloELoss/loss=16.5, PPYoloELoss/loss_cls=14.2, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   0%|          | 6/1793 [01:16<6:15:17, 12.60s/it, PPYoloELoss/loss=16.5, PPYoloELoss/loss_cls=14.2, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   0%|          | 6/1793 [01:28<6:15:17, 12.60s/it, PPYoloELoss/loss=15.6, PPYoloELoss/loss_cls=13.2, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   0%|          | 7/1793 [01:28<6:12:36, 12.52s/it, PPYoloELoss/loss=15.6, PPYoloELoss/loss_cls=13.2, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   0%|          | 7/1793 [01:40<6:12:36, 12.52s/it, PPYoloELoss/loss=14.6, PPYoloELoss/loss_cls=12.2, PPYoloELoss/loss_dfl=1.19, PPYoloELoss/loss_iou=1.21, gpu_mem=0]
Train epoch 0:   0%|          | 8/1793 [01:40<6:10:19, 12.45s/it, PPYoloELoss/loss=14.6, PPYoloELoss/loss_cls=12.2, PPYoloELoss/loss_dfl=1.19, PPYoloELoss/loss_iou=1.21, gpu_mem=0]
Train epoch 0:   0%|          | 8/1793 [01:53<6:10:19, 12.45s/it, PPYoloELoss/loss=13.7, PPYoloELoss/loss_cls=11.3, PPYoloELoss/loss_dfl=1.2, PPYoloELoss/loss_iou=1.21, gpu_mem=0] 
Train epoch 0:   1%|          | 9/1793 [01:53<6:11:30, 12.49s/it, PPYoloELoss/loss=13.7, PPYoloELoss/loss_cls=11.3, PPYoloELoss/loss_dfl=1.2, PPYoloELoss/loss_iou=1.21, gpu_mem=0]
Train epoch 0:   1%|          | 9/1793 [02:05<6:11:30, 12.49s/it, PPYoloELoss/loss=13, PPYoloELoss/loss_cls=10.6, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.19, gpu_mem=0] 
Train epoch 0:   1%|          | 10/1793 [02:05<6:11:22, 12.50s/it, PPYoloELoss/loss=13, PPYoloELoss/loss_cls=10.6, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 10/1793 [02:18<6:11:22, 12.50s/it, PPYoloELoss/loss=12.3, PPYoloELoss/loss_cls=9.97, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 11/1793 [02:18<6:09:53, 12.45s/it, PPYoloELoss/loss=12.3, PPYoloELoss/loss_cls=9.97, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 11/1793 [02:30<6:09:53, 12.45s/it, PPYoloELoss/loss=11.8, PPYoloELoss/loss_cls=9.45, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 12/1793 [02:30<6:08:47, 12.42s/it, PPYoloELoss/loss=11.8, PPYoloELoss/loss_cls=9.45, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 12/1793 [02:42<6:08:47, 12.42s/it, PPYoloELoss/loss=11.3, PPYoloELoss/loss_cls=8.97, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 13/1793 [02:42<6:08:38, 12.43s/it, PPYoloELoss/loss=11.3, PPYoloELoss/loss_cls=8.97, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 13/1793 [02:55<6:08:38, 12.43s/it, PPYoloELoss/loss=10.9, PPYoloELoss/loss_cls=8.54, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.2, gpu_mem=0] 
Train epoch 0:   1%|          | 14/1793 [02:55<6:08:06, 12.41s/it, PPYoloELoss/loss=10.9, PPYoloELoss/loss_cls=8.54, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.2, gpu_mem=0]
Train epoch 0:   1%|          | 14/1793 [03:07<6:08:06, 12.41s/it, PPYoloELoss/loss=10.6, PPYoloELoss/loss_cls=8.18, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.2, gpu_mem=0]
Train epoch 0:   1%|          | 15/1793 [03:07<6:06:34, 12.37s/it, PPYoloELoss/loss=10.6, PPYoloELoss/loss_cls=8.18, PPYoloELoss/loss_dfl=1.18, PPYoloELoss/loss_iou=1.2, gpu_mem=0]
Train epoch 0:   1%|          | 15/1793 [03:20<6:06:34, 12.37s/it, PPYoloELoss/loss=10.2, PPYoloELoss/loss_cls=7.84, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 16/1793 [03:20<6:07:17, 12.40s/it, PPYoloELoss/loss=10.2, PPYoloELoss/loss_cls=7.84, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 16/1793 [03:32<6:07:17, 12.40s/it, PPYoloELoss/loss=9.9, PPYoloELoss/loss_cls=7.53, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.19, gpu_mem=0] 
Train epoch 0:   1%|          | 17/1793 [03:32<6:07:07, 12.40s/it, PPYoloELoss/loss=9.9, PPYoloELoss/loss_cls=7.53, PPYoloELoss/loss_dfl=1.17, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 17/1793 [03:44<6:07:07, 12.40s/it, PPYoloELoss/loss=9.61, PPYoloELoss/loss_cls=7.26, PPYoloELoss/loss_dfl=1.16, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 18/1793 [03:44<6:05:51, 12.37s/it, PPYoloELoss/loss=9.61, PPYoloELoss/loss_cls=7.26, PPYoloELoss/loss_dfl=1.16, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   1%|          | 18/1793 [03:57<6:05:51, 12.37s/it, PPYoloELoss/loss=9.33, PPYoloELoss/loss_cls=7, PPYoloELoss/loss_dfl=1.16, PPYoloELoss/loss_iou=1.18, gpu_mem=0]   
Train epoch 0:   1%|          | 19/1793 [03:57<6:06:28, 12.39s/it, PPYoloELoss/loss=9.33, PPYoloELoss/loss_cls=7, PPYoloELoss/loss_dfl=1.16, PPYoloELoss/loss_iou=1.18, gpu_mem=0]
Train epoch 0:   1%|          | 19/1793 [04:09<6:06:28, 12.39s/it, PPYoloELoss/loss=9.09, PPYoloELoss/loss_cls=6.76, PPYoloELoss/loss_dfl=1.15, PPYoloELoss/loss_iou=1.17, gpu_mem=0]
Train epoch 0:   1%|          | 20/1793 [04:09<6:06:14, 12.39s/it, PPYoloELoss/loss=9.09, PPYoloELoss/loss_cls=6.76, PPYoloELoss/loss_dfl=1.15, PPYoloELoss/loss_iou=1.17, gpu_mem=0]
Train epoch 0:   1%|          | 20/1793 [04:21<6:06:14, 12.39s/it, PPYoloELoss/loss=8.88, PPYoloELoss/loss_cls=6.55, PPYoloELoss/loss_dfl=1.15, PPYoloELoss/loss_iou=1.18, gpu_mem=0]
Train epoch 0:   1%|          | 21/1793 [04:21<6:05:01, 12.36s/it, PPYoloELoss/loss=8.88, PPYoloELoss/loss_cls=6.55, PPYoloELoss/loss_dfl=1.15, PPYoloELoss/loss_iou=1.18, gpu_mem=0]
Train epoch 0:   1%|          | 21/1793 [04:34<6:05:01, 12.36s/it, PPYoloELoss/loss=8.68, PPYoloELoss/loss_cls=6.36, PPYoloELoss/loss_dfl=1.15, PPYoloELoss/loss_iou=1.18, gpu_mem=0]
Train epoch 0:   1%|          | 22/1793 [04:34<6:06:20, 12.41s/it, PPYoloELoss/loss=8.68, PPYoloELoss/loss_cls=6.36, PPYoloELoss/loss_dfl=1.15, PPYoloELoss/loss_iou=1.18, gpu_mem=0]
Train epoch 0:   1%|          | 22/1793 [04:46<6:06:20, 12.41s/it, PPYoloELoss/loss=8.49, PPYoloELoss/loss_cls=6.18, PPYoloELoss/loss_dfl=1.14, PPYoloELoss/loss_iou=1.17, gpu_mem=0]
Train epoch 0:   1%|▏         | 23/1793 [04:46<6:06:24, 12.42s/it, PPYoloELoss/loss=8.49, PPYoloELoss/loss_cls=6.18, PPYoloELoss/loss_dfl=1.14, PPYoloELoss/loss_iou=1.17, gpu_mem=0]
Train epoch 0:   1%|▏         | 23/1793 [04:59<6:06:24, 12.42s/it, PPYoloELoss/loss=8.32, PPYoloELoss/loss_cls=6.02, PPYoloELoss/loss_dfl=1.14, PPYoloELoss/loss_iou=1.17, gpu_mem=0]
Train epoch 0:   1%|▏         | 24/1793 [04:59<6:04:55, 12.38s/it, PPYoloELoss/loss=8.32, PPYoloELoss/loss_cls=6.02, PPYoloELoss/loss_dfl=1.14, PPYoloELoss/loss_iou=1.17, gpu_mem=0]
Train epoch 0:   1%|▏         | 24/1793 [05:11<6:04:55, 12.38s/it, PPYoloELoss/loss=8.17, PPYoloELoss/loss_cls=5.86, PPYoloELoss/loss_dfl=1.14, PPYoloELoss/loss_iou=1.17, gpu_mem=0]
Train epoch 0:   1%|▏         | 25/1793 [05:11<6:03:51, 12.35s/it, PPYoloELoss/loss=8.17, PPYoloELoss/loss_cls=5.86, PPYoloELoss/loss_dfl=1.14, PPYoloELoss/loss_iou=1.17, gpu_mem=0]
Train epoch 0:   1%|▏         | 25/1793 [05:23<6:03:51, 12.35s/it, PPYoloELoss/loss=8.02, PPYoloELoss/loss_cls=5.72, PPYoloELoss/loss_dfl=1.14, PPYoloELoss/loss_iou=1.16, gpu_mem=0]
Train epoch 0:   1%|▏         | 26/1793 [05:23<6:03:35, 12.35s/it, PPYoloELoss/loss=8.02, PPYoloELoss/loss_cls=5.72, PPYoloELoss/loss_dfl=1.14, PPYoloELoss/loss_iou=1.16, gpu_mem=0]
Train epoch 0:   1%|▏         | 26/1793 [05:36<6:03:35, 12.35s/it, PPYoloELoss/loss=7.88, PPYoloELoss/loss_cls=5.58, PPYoloELoss/loss_dfl=1.14, PPYoloELoss/loss_iou=1.16, gpu_mem=0]
Train epoch 0:   2%|▏         | 27/1793 [05:36<6:03:55, 12.36s/it, PPYoloELoss/loss=7.88, PPYoloELoss/loss_cls=5.58, PPYoloELoss/loss_dfl=1.14, PPYoloELoss/loss_iou=1.16, gpu_mem=0]
Train epoch 0:   2%|▏         | 27/1793 [05:48<6:03:55, 12.36s/it, PPYoloELoss/loss=7.75, PPYoloELoss/loss_cls=5.45, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.16, gpu_mem=0]
Train epoch 0:   2%|▏         | 28/1793 [05:48<6:03:06, 12.34s/it, PPYoloELoss/loss=7.75, PPYoloELoss/loss_cls=5.45, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.16, gpu_mem=0]
Train epoch 0:   2%|▏         | 28/1793 [06:00<6:03:06, 12.34s/it, PPYoloELoss/loss=7.62, PPYoloELoss/loss_cls=5.34, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.16, gpu_mem=0]
Train epoch 0:   2%|▏         | 29/1793 [06:00<6:03:01, 12.35s/it, PPYoloELoss/loss=7.62, PPYoloELoss/loss_cls=5.34, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.16, gpu_mem=0]
Train epoch 0:   2%|▏         | 29/1793 [06:13<6:03:01, 12.35s/it, PPYoloELoss/loss=7.5, PPYoloELoss/loss_cls=5.23, PPYoloELoss/loss_dfl=1.12, PPYoloELoss/loss_iou=1.15, gpu_mem=0] 
Train epoch 0:   2%|▏         | 30/1793 [06:13<6:04:41, 12.41s/it, PPYoloELoss/loss=7.5, PPYoloELoss/loss_cls=5.23, PPYoloELoss/loss_dfl=1.12, PPYoloELoss/loss_iou=1.15, gpu_mem=0]
Train epoch 0:   2%|▏         | 30/1793 [06:25<6:04:41, 12.41s/it, PPYoloELoss/loss=7.4, PPYoloELoss/loss_cls=5.13, PPYoloELoss/loss_dfl=1.12, PPYoloELoss/loss_iou=1.15, gpu_mem=0]
Train epoch 0:   2%|▏         | 31/1793 [06:25<6:05:44, 12.45s/it, PPYoloELoss/loss=7.4, PPYoloELoss/loss_cls=5.13, PPYoloELoss/loss_dfl=1.12, PPYoloELoss/loss_iou=1.15, gpu_mem=0]
Train epoch 0:   2%|▏         | 31/1793 [06:38<6:05:44, 12.45s/it, PPYoloELoss/loss=7.3, PPYoloELoss/loss_cls=5.03, PPYoloELoss/loss_dfl=1.11, PPYoloELoss/loss_iou=1.15, gpu_mem=0]
Train epoch 0:   2%|▏         | 32/1793 [06:38<6:05:37, 12.46s/it, PPYoloELoss/loss=7.3, PPYoloELoss/loss_cls=5.03, PPYoloELoss/loss_dfl=1.11, PPYoloELoss/loss_iou=1.15, gpu_mem=0]
Train epoch 0:   2%|▏         | 32/1793 [06:50<6:05:37, 12.46s/it, PPYoloELoss/loss=7.2, PPYoloELoss/loss_cls=4.94, PPYoloELoss/loss_dfl=1.11, PPYoloELoss/loss_iou=1.15, gpu_mem=0]
Train epoch 0:   2%|▏         | 33/1793 [06:50<6:04:46, 12.44s/it, PPYoloELoss/loss=7.2, PPYoloELoss/loss_cls=4.94, PPYoloELoss/loss_dfl=1.11, PPYoloELoss/loss_iou=1.15, gpu_mem=0]
Train epoch 0:   2%|▏         | 33/1793 [07:03<6:04:46, 12.44s/it, PPYoloELoss/loss=7.11, PPYoloELoss/loss_cls=4.86, PPYoloELoss/loss_dfl=1.11, PPYoloELoss/loss_iou=1.14, gpu_mem=0]
Train epoch 0:   2%|▏         | 34/1793 [07:03<6:04:44, 12.44s/it, PPYoloELoss/loss=7.11, PPYoloELoss/loss_cls=4.86, PPYoloELoss/loss_dfl=1.11, PPYoloELoss/loss_iou=1.14, gpu_mem=0]
Train epoch 0:   2%|▏         | 34/1793 [07:15<6:04:44, 12.44s/it, PPYoloELoss/loss=7.02, PPYoloELoss/loss_cls=4.78, PPYoloELoss/loss_dfl=1.11, PPYoloELoss/loss_iou=1.14, gpu_mem=0]
Train epoch 0:   2%|▏         | 35/1793 [07:15<6:07:00, 12.53s/it, PPYoloELoss/loss=7.02, PPYoloELoss/loss_cls=4.78, PPYoloELoss/loss_dfl=1.11, PPYoloELoss/loss_iou=1.14, gpu_mem=0]
Train epoch 0:   2%|▏         | 35/1793 [07:28<6:07:00, 12.53s/it, PPYoloELoss/loss=6.94, PPYoloELoss/loss_cls=4.69, PPYoloELoss/loss_dfl=1.1, PPYoloELoss/loss_iou=1.14, gpu_mem=0] 
Train epoch 0:   2%|▏         | 36/1793 [07:28<6:06:24, 12.51s/it, PPYoloELoss/loss=6.94, PPYoloELoss/loss_cls=4.69, PPYoloELoss/loss_dfl=1.1, PPYoloELoss/loss_iou=1.14, gpu_mem=0]
Train epoch 0:   2%|▏         | 36/1793 [07:40<6:06:24, 12.51s/it, PPYoloELoss/loss=6.85, PPYoloELoss/loss_cls=4.62, PPYoloELoss/loss_dfl=1.1, PPYoloELoss/loss_iou=1.14, gpu_mem=0]
Train epoch 0:   2%|▏         | 37/1793 [07:40<6:05:55, 12.50s/it, PPYoloELoss/loss=6.85, PPYoloELoss/loss_cls=4.62, PPYoloELoss/loss_dfl=1.1, PPYoloELoss/loss_iou=1.14, gpu_mem=0]
Train epoch 0:   2%|▏         | 37/1793 [07:53<6:05:55, 12.50s/it, PPYoloELoss/loss=6.78, PPYoloELoss/loss_cls=4.55, PPYoloELoss/loss_dfl=1.1, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   2%|▏         | 38/1793 [07:53<6:04:13, 12.45s/it, PPYoloELoss/loss=6.78, PPYoloELoss/loss_cls=4.55, PPYoloELoss/loss_dfl=1.1, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   2%|▏         | 38/1793 [08:05<6:04:13, 12.45s/it, PPYoloELoss/loss=6.7, PPYoloELoss/loss_cls=4.47, PPYoloELoss/loss_dfl=1.09, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   2%|▏         | 39/1793 [08:05<6:02:30, 12.40s/it, PPYoloELoss/loss=6.7, PPYoloELoss/loss_cls=4.47, PPYoloELoss/loss_dfl=1.09, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   2%|▏         | 39/1793 [08:17<6:02:30, 12.40s/it, PPYoloELoss/loss=6.63, PPYoloELoss/loss_cls=4.41, PPYoloELoss/loss_dfl=1.09, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   2%|▏         | 40/1793 [08:17<6:01:24, 12.37s/it, PPYoloELoss/loss=6.63, PPYoloELoss/loss_cls=4.41, PPYoloELoss/loss_dfl=1.09, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   2%|▏         | 40/1793 [08:30<6:01:24, 12.37s/it, PPYoloELoss/loss=6.57, PPYoloELoss/loss_cls=4.35, PPYoloELoss/loss_dfl=1.09, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   2%|▏         | 41/1793 [08:30<5:59:55, 12.33s/it, PPYoloELoss/loss=6.57, PPYoloELoss/loss_cls=4.35, PPYoloELoss/loss_dfl=1.09, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   2%|▏         | 41/1793 [08:42<5:59:55, 12.33s/it, PPYoloELoss/loss=6.5, PPYoloELoss/loss_cls=4.29, PPYoloELoss/loss_dfl=1.09, PPYoloELoss/loss_iou=1.13, gpu_mem=0] 
Train epoch 0:   2%|▏         | 42/1793 [08:42<5:58:07, 12.27s/it, PPYoloELoss/loss=6.5, PPYoloELoss/loss_cls=4.29, PPYoloELoss/loss_dfl=1.09, PPYoloELoss/loss_iou=1.13, gpu_mem=0]
Train epoch 0:   2%|▏         | 42/1793 [08:54<5:58:07, 12.27s/it, PPYoloELoss/loss=6.44, PPYoloELoss/loss_cls=4.24, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   2%|▏         | 43/1793 [08:54<5:57:19, 12.25s/it, PPYoloELoss/loss=6.44, PPYoloELoss/loss_cls=4.24, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   2%|▏         | 43/1793 [09:06<5:57:19, 12.25s/it, PPYoloELoss/loss=6.39, PPYoloELoss/loss_cls=4.18, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   2%|▏         | 44/1793 [09:06<5:57:29, 12.26s/it, PPYoloELoss/loss=6.39, PPYoloELoss/loss_cls=4.18, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   2%|▏         | 44/1793 [09:18<5:57:29, 12.26s/it, PPYoloELoss/loss=6.34, PPYoloELoss/loss_cls=4.13, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 45/1793 [09:18<5:56:38, 12.24s/it, PPYoloELoss/loss=6.34, PPYoloELoss/loss_cls=4.13, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 45/1793 [09:31<5:56:38, 12.24s/it, PPYoloELoss/loss=6.29, PPYoloELoss/loss_cls=4.09, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 46/1793 [09:31<5:57:03, 12.26s/it, PPYoloELoss/loss=6.29, PPYoloELoss/loss_cls=4.09, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 46/1793 [09:43<5:57:03, 12.26s/it, PPYoloELoss/loss=6.24, PPYoloELoss/loss_cls=4.04, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 47/1793 [09:43<5:57:13, 12.28s/it, PPYoloELoss/loss=6.24, PPYoloELoss/loss_cls=4.04, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 47/1793 [09:55<5:57:13, 12.28s/it, PPYoloELoss/loss=6.19, PPYoloELoss/loss_cls=4, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.12, gpu_mem=0]   
Train epoch 0:   3%|▎         | 48/1793 [09:55<5:56:51, 12.27s/it, PPYoloELoss/loss=6.19, PPYoloELoss/loss_cls=4, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 48/1793 [10:08<5:56:51, 12.27s/it, PPYoloELoss/loss=6.15, PPYoloELoss/loss_cls=3.95, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 49/1793 [10:08<5:56:51, 12.28s/it, PPYoloELoss/loss=6.15, PPYoloELoss/loss_cls=3.95, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 49/1793 [10:20<5:56:51, 12.28s/it, PPYoloELoss/loss=6.1, PPYoloELoss/loss_cls=3.91, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.12, gpu_mem=0] 
Train epoch 0:   3%|▎         | 50/1793 [10:20<5:56:55, 12.29s/it, PPYoloELoss/loss=6.1, PPYoloELoss/loss_cls=3.91, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 50/1793 [10:32<5:56:55, 12.29s/it, PPYoloELoss/loss=6.06, PPYoloELoss/loss_cls=3.87, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 51/1793 [10:32<5:57:14, 12.30s/it, PPYoloELoss/loss=6.06, PPYoloELoss/loss_cls=3.87, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   3%|▎         | 51/1793 [10:44<5:57:14, 12.30s/it, PPYoloELoss/loss=6.01, PPYoloELoss/loss_cls=3.83, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 52/1793 [10:44<5:56:29, 12.29s/it, PPYoloELoss/loss=6.01, PPYoloELoss/loss_cls=3.83, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 52/1793 [10:57<5:56:29, 12.29s/it, PPYoloELoss/loss=5.97, PPYoloELoss/loss_cls=3.8, PPYoloELoss/loss_dfl=1.06, PPYoloELoss/loss_iou=1.11, gpu_mem=0] 
Train epoch 0:   3%|▎         | 53/1793 [10:57<5:56:09, 12.28s/it, PPYoloELoss/loss=5.97, PPYoloELoss/loss_cls=3.8, PPYoloELoss/loss_dfl=1.06, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 53/1793 [11:09<5:56:09, 12.28s/it, PPYoloELoss/loss=5.94, PPYoloELoss/loss_cls=3.76, PPYoloELoss/loss_dfl=1.06, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 54/1793 [11:09<5:55:35, 12.27s/it, PPYoloELoss/loss=5.94, PPYoloELoss/loss_cls=3.76, PPYoloELoss/loss_dfl=1.06, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 54/1793 [11:21<5:55:35, 12.27s/it, PPYoloELoss/loss=5.89, PPYoloELoss/loss_cls=3.73, PPYoloELoss/loss_dfl=1.06, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 55/1793 [11:21<5:55:00, 12.26s/it, PPYoloELoss/loss=5.89, PPYoloELoss/loss_cls=3.73, PPYoloELoss/loss_dfl=1.06, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 55/1793 [11:33<5:55:00, 12.26s/it, PPYoloELoss/loss=5.86, PPYoloELoss/loss_cls=3.69, PPYoloELoss/loss_dfl=1.06, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 56/1793 [11:33<5:54:37, 12.25s/it, PPYoloELoss/loss=5.86, PPYoloELoss/loss_cls=3.69, PPYoloELoss/loss_dfl=1.06, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 56/1793 [11:45<5:54:37, 12.25s/it, PPYoloELoss/loss=5.82, PPYoloELoss/loss_cls=3.66, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 57/1793 [11:45<5:52:28, 12.18s/it, PPYoloELoss/loss=5.82, PPYoloELoss/loss_cls=3.66, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 57/1793 [11:58<5:52:28, 12.18s/it, PPYoloELoss/loss=5.78, PPYoloELoss/loss_cls=3.63, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 58/1793 [11:58<5:53:41, 12.23s/it, PPYoloELoss/loss=5.78, PPYoloELoss/loss_cls=3.63, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   3%|▎         | 58/1793 [12:10<5:53:41, 12.23s/it, PPYoloELoss/loss=5.74, PPYoloELoss/loss_cls=3.59, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.1, gpu_mem=0] 
Train epoch 0:   3%|▎         | 59/1793 [12:10<5:52:05, 12.18s/it, PPYoloELoss/loss=5.74, PPYoloELoss/loss_cls=3.59, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   3%|▎         | 59/1793 [12:22<5:52:05, 12.18s/it, PPYoloELoss/loss=5.71, PPYoloELoss/loss_cls=3.56, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   3%|▎         | 60/1793 [12:22<5:52:54, 12.22s/it, PPYoloELoss/loss=5.71, PPYoloELoss/loss_cls=3.56, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   3%|▎         | 60/1793 [12:35<5:52:54, 12.22s/it, PPYoloELoss/loss=5.68, PPYoloELoss/loss_cls=3.53, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   3%|▎         | 61/1793 [12:35<5:53:48, 12.26s/it, PPYoloELoss/loss=5.68, PPYoloELoss/loss_cls=3.53, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   3%|▎         | 61/1793 [12:47<5:53:48, 12.26s/it, PPYoloELoss/loss=5.64, PPYoloELoss/loss_cls=3.5, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0] 
Train epoch 0:   3%|▎         | 62/1793 [12:47<5:53:31, 12.25s/it, PPYoloELoss/loss=5.64, PPYoloELoss/loss_cls=3.5, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   3%|▎         | 62/1793 [12:59<5:53:31, 12.25s/it, PPYoloELoss/loss=5.62, PPYoloELoss/loss_cls=3.48, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   4%|▎         | 63/1793 [12:59<5:52:52, 12.24s/it, PPYoloELoss/loss=5.62, PPYoloELoss/loss_cls=3.48, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   4%|▎         | 63/1793 [13:11<5:52:52, 12.24s/it, PPYoloELoss/loss=5.59, PPYoloELoss/loss_cls=3.45, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   4%|▎         | 64/1793 [13:11<5:52:12, 12.22s/it, PPYoloELoss/loss=5.59, PPYoloELoss/loss_cls=3.45, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   4%|▎         | 64/1793 [13:23<5:52:12, 12.22s/it, PPYoloELoss/loss=5.56, PPYoloELoss/loss_cls=3.43, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   4%|▎         | 65/1793 [13:23<5:50:59, 12.19s/it, PPYoloELoss/loss=5.56, PPYoloELoss/loss_cls=3.43, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   4%|▎         | 65/1793 [13:36<5:50:59, 12.19s/it, PPYoloELoss/loss=5.53, PPYoloELoss/loss_cls=3.4, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0] 
Train epoch 0:   4%|▎         | 66/1793 [13:36<5:51:36, 12.22s/it, PPYoloELoss/loss=5.53, PPYoloELoss/loss_cls=3.4, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   4%|▎         | 66/1793 [13:48<5:51:36, 12.22s/it, PPYoloELoss/loss=5.51, PPYoloELoss/loss_cls=3.38, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▎         | 67/1793 [13:48<5:51:18, 12.21s/it, PPYoloELoss/loss=5.51, PPYoloELoss/loss_cls=3.38, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▎         | 67/1793 [14:00<5:51:18, 12.21s/it, PPYoloELoss/loss=5.48, PPYoloELoss/loss_cls=3.35, PPYoloELoss/loss_dfl=1.03, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▍         | 68/1793 [14:00<5:51:34, 12.23s/it, PPYoloELoss/loss=5.48, PPYoloELoss/loss_cls=3.35, PPYoloELoss/loss_dfl=1.03, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▍         | 68/1793 [14:12<5:51:34, 12.23s/it, PPYoloELoss/loss=5.45, PPYoloELoss/loss_cls=3.33, PPYoloELoss/loss_dfl=1.03, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▍         | 69/1793 [14:12<5:50:34, 12.20s/it, PPYoloELoss/loss=5.45, PPYoloELoss/loss_cls=3.33, PPYoloELoss/loss_dfl=1.03, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▍         | 69/1793 [14:24<5:50:34, 12.20s/it, PPYoloELoss/loss=5.42, PPYoloELoss/loss_cls=3.3, PPYoloELoss/loss_dfl=1.03, PPYoloELoss/loss_iou=1.09, gpu_mem=0] 
Train epoch 0:   4%|▍         | 70/1793 [14:24<5:49:29, 12.17s/it, PPYoloELoss/loss=5.42, PPYoloELoss/loss_cls=3.3, PPYoloELoss/loss_dfl=1.03, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▍         | 70/1793 [14:37<5:49:29, 12.17s/it, PPYoloELoss/loss=5.4, PPYoloELoss/loss_cls=3.28, PPYoloELoss/loss_dfl=1.03, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▍         | 71/1793 [14:37<5:50:12, 12.20s/it, PPYoloELoss/loss=5.4, PPYoloELoss/loss_cls=3.28, PPYoloELoss/loss_dfl=1.03, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▍         | 71/1793 [14:49<5:50:12, 12.20s/it, PPYoloELoss/loss=5.38, PPYoloELoss/loss_cls=3.27, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▍         | 72/1793 [14:49<5:49:13, 12.18s/it, PPYoloELoss/loss=5.38, PPYoloELoss/loss_cls=3.27, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.09, gpu_mem=0]
Train epoch 0:   4%|▍         | 72/1793 [15:01<5:49:13, 12.18s/it, PPYoloELoss/loss=5.35, PPYoloELoss/loss_cls=3.25, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 73/1793 [15:01<5:49:20, 12.19s/it, PPYoloELoss/loss=5.35, PPYoloELoss/loss_cls=3.25, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 73/1793 [15:13<5:49:20, 12.19s/it, PPYoloELoss/loss=5.33, PPYoloELoss/loss_cls=3.22, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 74/1793 [15:13<5:49:28, 12.20s/it, PPYoloELoss/loss=5.33, PPYoloELoss/loss_cls=3.22, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 74/1793 [15:25<5:49:28, 12.20s/it, PPYoloELoss/loss=5.3, PPYoloELoss/loss_cls=3.2, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.08, gpu_mem=0]  
Train epoch 0:   4%|▍         | 75/1793 [15:25<5:49:15, 12.20s/it, PPYoloELoss/loss=5.3, PPYoloELoss/loss_cls=3.2, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 75/1793 [15:37<5:49:15, 12.20s/it, PPYoloELoss/loss=5.27, PPYoloELoss/loss_cls=3.18, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 76/1793 [15:37<5:49:14, 12.20s/it, PPYoloELoss/loss=5.27, PPYoloELoss/loss_cls=3.18, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 76/1793 [15:50<5:49:14, 12.20s/it, PPYoloELoss/loss=5.25, PPYoloELoss/loss_cls=3.16, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 77/1793 [15:50<5:50:05, 12.24s/it, PPYoloELoss/loss=5.25, PPYoloELoss/loss_cls=3.16, PPYoloELoss/loss_dfl=1.02, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 77/1793 [16:02<5:50:05, 12.24s/it, PPYoloELoss/loss=5.23, PPYoloELoss/loss_cls=3.14, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 78/1793 [16:02<5:51:18, 12.29s/it, PPYoloELoss/loss=5.23, PPYoloELoss/loss_cls=3.14, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 78/1793 [16:14<5:51:18, 12.29s/it, PPYoloELoss/loss=5.21, PPYoloELoss/loss_cls=3.12, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 79/1793 [16:14<5:50:20, 12.26s/it, PPYoloELoss/loss=5.21, PPYoloELoss/loss_cls=3.12, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 79/1793 [16:27<5:50:20, 12.26s/it, PPYoloELoss/loss=5.19, PPYoloELoss/loss_cls=3.1, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.08, gpu_mem=0] 
Train epoch 0:   4%|▍         | 80/1793 [16:27<5:50:30, 12.28s/it, PPYoloELoss/loss=5.19, PPYoloELoss/loss_cls=3.1, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.08, gpu_mem=0]
Train epoch 0:   4%|▍         | 80/1793 [16:39<5:50:30, 12.28s/it, PPYoloELoss/loss=5.17, PPYoloELoss/loss_cls=3.08, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 81/1793 [16:39<5:49:31, 12.25s/it, PPYoloELoss/loss=5.17, PPYoloELoss/loss_cls=3.08, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 81/1793 [16:51<5:49:31, 12.25s/it, PPYoloELoss/loss=5.14, PPYoloELoss/loss_cls=3.06, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 82/1793 [16:51<5:48:10, 12.21s/it, PPYoloELoss/loss=5.14, PPYoloELoss/loss_cls=3.06, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 82/1793 [17:03<5:48:10, 12.21s/it, PPYoloELoss/loss=5.12, PPYoloELoss/loss_cls=3.05, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 83/1793 [17:03<5:47:30, 12.19s/it, PPYoloELoss/loss=5.12, PPYoloELoss/loss_cls=3.05, PPYoloELoss/loss_dfl=1.01, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 83/1793 [17:15<5:47:30, 12.19s/it, PPYoloELoss/loss=5.1, PPYoloELoss/loss_cls=3.03, PPYoloELoss/loss_dfl=1, PPYoloELoss/loss_iou=1.07, gpu_mem=0]    
Train epoch 0:   5%|▍         | 84/1793 [17:15<5:47:22, 12.20s/it, PPYoloELoss/loss=5.1, PPYoloELoss/loss_cls=3.03, PPYoloELoss/loss_dfl=1, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 84/1793 [17:28<5:47:22, 12.20s/it, PPYoloELoss/loss=5.08, PPYoloELoss/loss_cls=3.01, PPYoloELoss/loss_dfl=1, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 85/1793 [17:28<5:47:27, 12.21s/it, PPYoloELoss/loss=5.08, PPYoloELoss/loss_cls=3.01, PPYoloELoss/loss_dfl=1, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 85/1793 [17:40<5:47:27, 12.21s/it, PPYoloELoss/loss=5.06, PPYoloELoss/loss_cls=2.99, PPYoloELoss/loss_dfl=1, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 86/1793 [17:40<5:47:52, 12.23s/it, PPYoloELoss/loss=5.06, PPYoloELoss/loss_cls=2.99, PPYoloELoss/loss_dfl=1, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 86/1793 [17:52<5:47:52, 12.23s/it, PPYoloELoss/loss=5.05, PPYoloELoss/loss_cls=2.98, PPYoloELoss/loss_dfl=1, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 87/1793 [17:52<5:49:01, 12.28s/it, PPYoloELoss/loss=5.05, PPYoloELoss/loss_cls=2.98, PPYoloELoss/loss_dfl=1, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 87/1793 [18:05<5:49:01, 12.28s/it, PPYoloELoss/loss=5.03, PPYoloELoss/loss_cls=2.96, PPYoloELoss/loss_dfl=1, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 88/1793 [18:05<5:49:09, 12.29s/it, PPYoloELoss/loss=5.03, PPYoloELoss/loss_cls=2.96, PPYoloELoss/loss_dfl=1, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 88/1793 [18:17<5:49:09, 12.29s/it, PPYoloELoss/loss=5.01, PPYoloELoss/loss_cls=2.95, PPYoloELoss/loss_dfl=0.999, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 89/1793 [18:17<5:48:50, 12.28s/it, PPYoloELoss/loss=5.01, PPYoloELoss/loss_cls=2.95, PPYoloELoss/loss_dfl=0.999, PPYoloELoss/loss_iou=1.07, gpu_mem=0]
Train epoch 0:   5%|▍         | 89/1793 [18:29<5:48:50, 12.28s/it, PPYoloELoss/loss=4.99, PPYoloELoss/loss_cls=2.93, PPYoloELoss/loss_dfl=0.997, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 90/1793 [18:29<5:47:41, 12.25s/it, PPYoloELoss/loss=4.99, PPYoloELoss/loss_cls=2.93, PPYoloELoss/loss_dfl=0.997, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 90/1793 [18:41<5:47:41, 12.25s/it, PPYoloELoss/loss=4.97, PPYoloELoss/loss_cls=2.92, PPYoloELoss/loss_dfl=0.995, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 91/1793 [18:41<5:47:43, 12.26s/it, PPYoloELoss/loss=4.97, PPYoloELoss/loss_cls=2.92, PPYoloELoss/loss_dfl=0.995, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 91/1793 [18:54<5:47:43, 12.26s/it, PPYoloELoss/loss=4.95, PPYoloELoss/loss_cls=2.9, PPYoloELoss/loss_dfl=0.994, PPYoloELoss/loss_iou=1.06, gpu_mem=0] 
Train epoch 0:   5%|▌         | 92/1793 [18:54<5:47:48, 12.27s/it, PPYoloELoss/loss=4.95, PPYoloELoss/loss_cls=2.9, PPYoloELoss/loss_dfl=0.994, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 92/1793 [19:06<5:47:48, 12.27s/it, PPYoloELoss/loss=4.94, PPYoloELoss/loss_cls=2.89, PPYoloELoss/loss_dfl=0.992, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 93/1793 [19:06<5:46:11, 12.22s/it, PPYoloELoss/loss=4.94, PPYoloELoss/loss_cls=2.89, PPYoloELoss/loss_dfl=0.992, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 93/1793 [19:18<5:46:11, 12.22s/it, PPYoloELoss/loss=4.92, PPYoloELoss/loss_cls=2.87, PPYoloELoss/loss_dfl=0.991, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 94/1793 [19:18<5:45:28, 12.20s/it, PPYoloELoss/loss=4.92, PPYoloELoss/loss_cls=2.87, PPYoloELoss/loss_dfl=0.991, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 94/1793 [19:30<5:45:28, 12.20s/it, PPYoloELoss/loss=4.9, PPYoloELoss/loss_cls=2.86, PPYoloELoss/loss_dfl=0.989, PPYoloELoss/loss_iou=1.06, gpu_mem=0] 
Train epoch 0:   5%|▌         | 95/1793 [19:30<5:45:19, 12.20s/it, PPYoloELoss/loss=4.9, PPYoloELoss/loss_cls=2.86, PPYoloELoss/loss_dfl=0.989, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 95/1793 [19:42<5:45:19, 12.20s/it, PPYoloELoss/loss=4.89, PPYoloELoss/loss_cls=2.84, PPYoloELoss/loss_dfl=0.989, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 96/1793 [19:42<5:43:12, 12.13s/it, PPYoloELoss/loss=4.89, PPYoloELoss/loss_cls=2.84, PPYoloELoss/loss_dfl=0.989, PPYoloELoss/loss_iou=1.06, gpu_mem=0]
Train epoch 0:   5%|▌         | 96/1793 [19:54<5:43:12, 12.13s/it, PPYoloELoss/loss=4.87, PPYoloELoss/loss_cls=2.83, PPYoloELoss/loss_dfl=0.987, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   5%|▌         | 97/1793 [19:54<5:43:09, 12.14s/it, PPYoloELoss/loss=4.87, PPYoloELoss/loss_cls=2.83, PPYoloELoss/loss_dfl=0.987, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   5%|▌         | 97/1793 [20:07<5:43:09, 12.14s/it, PPYoloELoss/loss=4.86, PPYoloELoss/loss_cls=2.82, PPYoloELoss/loss_dfl=0.986, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   5%|▌         | 98/1793 [20:07<5:44:37, 12.20s/it, PPYoloELoss/loss=4.86, PPYoloELoss/loss_cls=2.82, PPYoloELoss/loss_dfl=0.986, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   5%|▌         | 98/1793 [20:20<5:44:37, 12.20s/it, PPYoloELoss/loss=4.84, PPYoloELoss/loss_cls=2.81, PPYoloELoss/loss_dfl=0.985, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   6%|▌         | 99/1793 [20:20<5:57:15, 12.65s/it, PPYoloELoss/loss=4.84, PPYoloELoss/loss_cls=2.81, PPYoloELoss/loss_dfl=0.985, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   6%|▌         | 99/1793 [20:33<5:57:15, 12.65s/it, PPYoloELoss/loss=4.83, PPYoloELoss/loss_cls=2.8, PPYoloELoss/loss_dfl=0.983, PPYoloELoss/loss_iou=1.05, gpu_mem=0] 
Train epoch 0:   6%|▌         | 100/1793 [20:33<5:55:39, 12.60s/it, PPYoloELoss/loss=4.83, PPYoloELoss/loss_cls=2.8, PPYoloELoss/loss_dfl=0.983, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   6%|▌         | 100/1793 [20:47<5:55:39, 12.60s/it, PPYoloELoss/loss=4.82, PPYoloELoss/loss_cls=2.79, PPYoloELoss/loss_dfl=0.982, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   6%|▌         | 101/1793 [20:47<6:05:59, 12.98s/it, PPYoloELoss/loss=4.82, PPYoloELoss/loss_cls=2.79, PPYoloELoss/loss_dfl=0.982, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   6%|▌         | 101/1793 [28:54<6:05:59, 12.98s/it, PPYoloELoss/loss=4.8, PPYoloELoss/loss_cls=2.78, PPYoloELoss/loss_dfl=0.98, PPYoloELoss/loss_iou=1.05, gpu_mem=0]  
Train epoch 0:   6%|▌         | 102/1793 [28:54<72:54:04, 155.20s/it, PPYoloELoss/loss=4.8, PPYoloELoss/loss_cls=2.78, PPYoloELoss/loss_dfl=0.98, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   6%|▌         | 102/1793 [29:06<72:54:04, 155.20s/it, PPYoloELoss/loss=4.79, PPYoloELoss/loss_cls=2.76, PPYoloELoss/loss_dfl=0.978, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   6%|▌         | 103/1793 [29:06<52:41:24, 112.24s/it, PPYoloELoss/loss=4.79, PPYoloELoss/loss_cls=2.76, PPYoloELoss/loss_dfl=0.978, PPYoloELoss/loss_iou=1.05, gpu_mem=0]
Train epoch 0:   6%|▌         | 103/1793 [29:18<52:41:24, 112.24s/it, PPYoloELoss/loss=4.77, PPYoloELoss/loss_cls=2.75, PPYoloELoss/loss_dfl=0.977, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 104/1793 [29:18<38:34:11, 82.21s/it, PPYoloELoss/loss=4.77, PPYoloELoss/loss_cls=2.75, PPYoloELoss/loss_dfl=0.977, PPYoloELoss/loss_iou=1.04, gpu_mem=0] 
Train epoch 0:   6%|▌         | 104/1793 [29:30<38:34:11, 82.21s/it, PPYoloELoss/loss=4.76, PPYoloELoss/loss_cls=2.74, PPYoloELoss/loss_dfl=0.976, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 105/1793 [29:30<28:43:57, 61.28s/it, PPYoloELoss/loss=4.76, PPYoloELoss/loss_cls=2.74, PPYoloELoss/loss_dfl=0.976, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 105/1793 [29:44<28:43:57, 61.28s/it, PPYoloELoss/loss=4.75, PPYoloELoss/loss_cls=2.73, PPYoloELoss/loss_dfl=0.975, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 106/1793 [29:44<21:59:01, 46.91s/it, PPYoloELoss/loss=4.75, PPYoloELoss/loss_cls=2.73, PPYoloELoss/loss_dfl=0.975, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 106/1793 [45:45<21:59:01, 46.91s/it, PPYoloELoss/loss=4.73, PPYoloELoss/loss_cls=2.72, PPYoloELoss/loss_dfl=0.973, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 107/1793 [45:45<150:31:04, 321.39s/it, PPYoloELoss/loss=4.73, PPYoloELoss/loss_cls=2.72, PPYoloELoss/loss_dfl=0.973, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 107/1793 [45:57<150:31:04, 321.39s/it, PPYoloELoss/loss=4.72, PPYoloELoss/loss_cls=2.71, PPYoloELoss/loss_dfl=0.971, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 108/1793 [45:57<106:59:20, 228.58s/it, PPYoloELoss/loss=4.72, PPYoloELoss/loss_cls=2.71, PPYoloELoss/loss_dfl=0.971, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 108/1793 [46:10<106:59:20, 228.58s/it, PPYoloELoss/loss=4.7, PPYoloELoss/loss_cls=2.7, PPYoloELoss/loss_dfl=0.97, PPYoloELoss/loss_iou=1.04, gpu_mem=0]   
Train epoch 0:   6%|▌         | 109/1793 [46:10<76:33:25, 163.66s/it, PPYoloELoss/loss=4.7, PPYoloELoss/loss_cls=2.7, PPYoloELoss/loss_dfl=0.97, PPYoloELoss/loss_iou=1.04, gpu_mem=0] 
Train epoch 0:   6%|▌         | 109/1793 [46:23<76:33:25, 163.66s/it, PPYoloELoss/loss=4.69, PPYoloELoss/loss_cls=2.68, PPYoloELoss/loss_dfl=0.968, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 110/1793 [46:23<55:23:12, 118.47s/it, PPYoloELoss/loss=4.69, PPYoloELoss/loss_cls=2.68, PPYoloELoss/loss_dfl=0.968, PPYoloELoss/loss_iou=1.04, gpu_mem=0]
Train epoch 0:   6%|▌         | 110/1793 [46:36<55:23:12, 118.47s/it, PPYoloELoss/loss=4.68, PPYoloELoss/loss_cls=2.67, PPYoloELoss/loss_dfl=0.967, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▌         | 111/1793 [46:36<40:40:32, 87.06s/it, PPYoloELoss/loss=4.68, PPYoloELoss/loss_cls=2.67, PPYoloELoss/loss_dfl=0.967, PPYoloELoss/loss_iou=1.03, gpu_mem=0] 
Train epoch 0:   6%|▌         | 111/1793 [46:51<40:40:32, 87.06s/it, PPYoloELoss/loss=4.66, PPYoloELoss/loss_cls=2.67, PPYoloELoss/loss_dfl=0.965, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▌         | 112/1793 [46:51<30:31:16, 65.36s/it, PPYoloELoss/loss=4.66, PPYoloELoss/loss_cls=2.67, PPYoloELoss/loss_dfl=0.965, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▌         | 112/1793 [47:05<30:31:16, 65.36s/it, PPYoloELoss/loss=4.65, PPYoloELoss/loss_cls=2.65, PPYoloELoss/loss_dfl=0.964, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▋         | 113/1793 [47:05<23:14:25, 49.80s/it, PPYoloELoss/loss=4.65, PPYoloELoss/loss_cls=2.65, PPYoloELoss/loss_dfl=0.964, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▋         | 113/1793 [47:21<23:14:25, 49.80s/it, PPYoloELoss/loss=4.64, PPYoloELoss/loss_cls=2.64, PPYoloELoss/loss_dfl=0.963, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▋         | 114/1793 [47:21<18:31:05, 39.71s/it, PPYoloELoss/loss=4.64, PPYoloELoss/loss_cls=2.64, PPYoloELoss/loss_dfl=0.963, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▋         | 114/1793 [47:39<18:31:05, 39.71s/it, PPYoloELoss/loss=4.63, PPYoloELoss/loss_cls=2.64, PPYoloELoss/loss_dfl=0.962, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▋         | 115/1793 [47:39<15:26:48, 33.14s/it, PPYoloELoss/loss=4.63, PPYoloELoss/loss_cls=2.64, PPYoloELoss/loss_dfl=0.962, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▋         | 115/1793 [47:58<15:26:48, 33.14s/it, PPYoloELoss/loss=4.62, PPYoloELoss/loss_cls=2.63, PPYoloELoss/loss_dfl=0.961, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▋         | 116/1793 [47:58<13:32:57, 29.09s/it, PPYoloELoss/loss=4.62, PPYoloELoss/loss_cls=2.63, PPYoloELoss/loss_dfl=0.961, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   6%|▋         | 116/1793 [48:18<13:32:57, 29.09s/it, PPYoloELoss/loss=4.61, PPYoloELoss/loss_cls=2.62, PPYoloELoss/loss_dfl=0.96, PPYoloELoss/loss_iou=1.03, gpu_mem=0] 
Train epoch 0:   7%|▋         | 117/1793 [48:18<12:18:06, 26.42s/it, PPYoloELoss/loss=4.61, PPYoloELoss/loss_cls=2.62, PPYoloELoss/loss_dfl=0.96, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   7%|▋         | 117/1793 [49:40<12:18:06, 26.42s/it, PPYoloELoss/loss=4.6, PPYoloELoss/loss_cls=2.61, PPYoloELoss/loss_dfl=0.959, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   7%|▋         | 118/1793 [49:40<20:03:00, 43.09s/it, PPYoloELoss/loss=4.6, PPYoloELoss/loss_cls=2.61, PPYoloELoss/loss_dfl=0.959, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   7%|▋         | 118/1793 [49:53<20:03:00, 43.09s/it, PPYoloELoss/loss=4.59, PPYoloELoss/loss_cls=2.6, PPYoloELoss/loss_dfl=0.957, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   7%|▋         | 119/1793 [49:53<15:46:05, 33.91s/it, PPYoloELoss/loss=4.59, PPYoloELoss/loss_cls=2.6, PPYoloELoss/loss_dfl=0.957, PPYoloELoss/loss_iou=1.03, gpu_mem=0]
Train epoch 0:   7%|▋         | 119/1793 [50:07<15:46:05, 33.91s/it, PPYoloELoss/loss=4.57, PPYoloELoss/loss_cls=2.59, PPYoloELoss/loss_dfl=0.956, PPYoloELoss/loss_iou=1.02, gpu_mem=0]
Train epoch 0:   7%|▋         | 120/1793 [50:07<13:00:48, 28.00s/it, PPYoloELoss/loss=4.57, PPYoloELoss/loss_cls=2.59, PPYoloELoss/loss_dfl=0.956, PPYoloELoss/loss_iou=1.02, gpu_mem=0]
Train epoch 0:   7%|▋         | 120/1793 [50:20<13:00:48, 28.00s/it, PPYoloELoss/loss=4.56, PPYoloELoss/loss_cls=2.58, PPYoloELoss/loss_dfl=0.955, PPYoloELoss/loss_iou=1.02, gpu_mem=0]
Train epoch 0:   7%|▋         | 121/1793 [50:20<10:57:21, 23.59s/it, PPYoloELoss/loss=4.56, PPYoloELoss/loss_cls=2.58, PPYoloELoss/loss_dfl=0.955, PPYoloELoss/loss_iou=1.02, gpu_mem=0]
Train epoch 0:   7%|▋         | 121/1793 [50:33<10:57:21, 23.59s/it, PPYoloELoss/loss=4.55, PPYoloELoss/loss_cls=2.58, PPYoloELoss/loss_dfl=0.953, PPYoloELoss/loss_iou=1.02, gpu_mem=0]
Train epoch 0:   7%|▋         | 122/1793 [50:33<9:23:58, 20.25s/it, PPYoloELoss/loss=4.55, PPYoloELoss/loss_cls=2.58, PPYoloELoss/loss_dfl=0.953, PPYoloELoss/loss_iou=1.02, gpu_mem=0] 
Train epoch 0:   7%|▋         | 122/1793 [50:34<11:32:39, 24.87s/it, PPYoloELoss/loss=4.55, PPYoloELoss/loss_cls=2.58, PPYoloELoss/loss_dfl=0.953, PPYoloELoss/loss_iou=1.02, gpu_mem=0]
[2025-06-01 18:41:44] INFO - sg_trainer.py - 
[MODEL TRAINING EXECUTION HAS BEEN INTERRUPTED]... Please wait until SOFT-TERMINATION process finishes and saves all of the Model Checkpoints and log files before terminating...
[2025-06-01 18:41:44] INFO - sg_trainer.py - For HARD Termination - Stop the process again
[2025-06-01 18:41:44] INFO - base_sg_logger.py - [CLEANUP] - Successfully stopped system monitoring process

🎉 Training completed successfully!
⏱️ Total time: 0.85 hours
📊 Results: checkpoints/yolo_nas_s_barcode_20250601_175045/
📈 TensorBoard: tensorboard --logdir checkpoints/yolo_nas_s_barcode_20250601_175045/

🎯 Next steps:
1. Evaluate model performance
2. Export to ONNX/CoreML for deployment
3. Test inference on new barcode images
Exception ignored in: <function _MultiProcessingDataLoaderIter.__del__ at 0x1053dcae0>
Traceback (most recent call last):
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 1663, in __del__
    self._shutdown_workers()
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 1627, in _shutdown_workers
    w.join(timeout=_utils.MP_STATUS_CHECK_INTERVAL)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/process.py", line 149, in join
    res = self._popen.wait(timeout)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/popen_fork.py", line 40, in wait
    if not wait([self.sentinel], timeout):
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/connection.py", line 948, in wait
    ready = selector.select(timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/selectors.py", line 415, in select
    fd_event_list = self._selector.poll(timeout)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt: 
Exception ignored in: <function _MultiProcessingDataLoaderIter.__del__ at 0x1053dcae0>
Traceback (most recent call last):
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 1663, in __del__
    self._shutdown_workers()
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 1627, in _shutdown_workers
    w.join(timeout=_utils.MP_STATUS_CHECK_INTERVAL)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/process.py", line 149, in join
    res = self._popen.wait(timeout)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/popen_fork.py", line 40, in wait
    if not wait([self.sentinel], timeout):
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/connection.py", line 948, in wait
    ready = selector.select(timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/selectors.py", line 415, in select
    fd_event_list = self._selector.poll(timeout)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt: 
