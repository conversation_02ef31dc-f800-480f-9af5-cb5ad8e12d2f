[2025-06-01 17:50:37] INFO - super_gradients.common.crash_handler.crash_tips_setup - Crash tips is enabled. You can set your environment variable to CRASH_HANDLER=FALSE to disable it
[2025-06-01 17:50:38] DEBUG - matplotlib - matplotlib data path: /Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/matplotlib/mpl-data
[2025-06-01 17:50:38] DEBUG - matplotlib - CONFIGDIR=/Users/<USER>/.matplotlib
[2025-06-01 17:50:38] DEBUG - matplotlib - interactive is False
[2025-06-01 17:50:38] DEBUG - matplotlib - platform is darwin
[2025-06-01 17:50:38] DEBUG - matplotlib - CACHEDIR=/Users/<USER>/.matplotlib
[2025-06-01 17:50:38] DEBUG - matplotlib.font_manager - Using fontManager instance from /Users/<USER>/.matplotlib/fontlist-v390.json
[2025-06-01 17:50:38] DEBUG - super_gradients.common.sg_loggers.clearml_sg_logger - Failed to import clearml
[2025-06-01 17:50:38] DEBUG - git.cmd - Popen(['git', 'version'], cwd=/Volumes/DATA/projects/Yolo-nas, stdin=None, shell=False, universal_newlines=False)
[2025-06-01 17:50:38] DEBUG - git.cmd - Popen(['git', 'version'], cwd=/Volumes/DATA/projects/Yolo-nas, stdin=None, shell=False, universal_newlines=False)
[2025-06-01 17:50:38] DEBUG - wandb.docker.auth - Trying paths: ['/Users/<USER>/.docker/config.json', '/Users/<USER>/.dockercfg']
[2025-06-01 17:50:38] DEBUG - wandb.docker.auth - Found file at path: /Users/<USER>/.docker/config.json
[2025-06-01 17:50:38] DEBUG - wandb.docker.auth - Found 'credsStore' section
[2025-06-01 17:50:38] DEBUG - hydra.core.utils - Setting JobRuntime:name=UNKNOWN_NAME
[2025-06-01 17:50:38] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:50:38] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:50:39] WARNING - super_gradients.sanity_check.env_sanity_check - [31mFailed to verify operating system: Deci officially supports only Linux kernels. Some features may not work as expected.[0m
[2025-06-01 17:50:39] DEBUG - super_gradients.sanity_check.env_sanity_check - setuptools==80.9.0 does not satisfy requirement setuptools<67.0.0,>=65.5.1
[2025-06-01 17:50:39] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:50:39] INFO - super_gradients.training.datasets.detection_datasets.detection_dataset - Dataset Initialization in progress. `cache_annotations=True` causes the process to take longer due to full dataset indexing.
[2025-06-01 17:50:44] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:50:44] INFO - super_gradients.training.datasets.detection_datasets.detection_dataset - Dataset Initialization in progress. `cache_annotations=True` causes the process to take longer due to full dataset indexing.
[2025-06-01 17:50:45] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:50:45] WARNING - super_gradients.training.utils.checkpoint_utils - :warning: The pre-trained models provided by SuperGradients may have their own licenses or terms and conditions derived from the dataset used for pre-training.
 It is your responsibility to determine whether you have permission to use the models for your use case.
 The model you have requested was pre-trained on the coco dataset, published under the following terms: https://cocodataset.org/#termsofuse
[2025-06-01 17:50:45] INFO - super_gradients.training.utils.checkpoint_utils - License Notification: YOLO-NAS pre-trained weights are subjected to the specific license terms and conditions detailed in 
https://github.com/Deci-AI/super-gradients/blob/master/LICENSE.YOLONAS.md
By downloading the pre-trained weight files you agree to comply with these terms.
[2025-06-01 17:50:45] INFO - super_gradients.training.utils.checkpoint_utils - Successfully loaded pretrained weights for architecture yolo_nas_s
[2025-06-01 17:50:45] DEBUG - super_gradients.training.utils.checkpoint_utils - Trying to load preprocessing params from checkpoint. Preprocessing params in checkpoint: False. Model YoloNAS_S inherit HasPredict: True
[2025-06-01 17:50:45] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:50:45] INFO - super_gradients.training.sg_trainer.sg_trainer - Starting a new run with `run_id=RUN_20250601_175045_952654`
[2025-06-01 17:50:45] INFO - super_gradients.training.sg_trainer.sg_trainer - Checkpoints directory: checkpoints/yolo_nas_s_barcode_20250601_175045/RUN_20250601_175045_952654
[2025-06-01 17:50:45] INFO - super_gradients.training.sg_trainer.sg_trainer - Using EMA with params {'decay': 0.9999}
[2025-06-01 17:50:45] WARNING - super_gradients.training.utils.ema - Parameter decay_type is not specified for EMA model. Please specify decay_type parameter explicitly in your config:
ema: True
ema_params: 
  decay: 0.9999
  decay_type: constant|exp|threshold
Will default to `exp` decay with beta = 15
In the next major release of SG this warning will become an error.
[2025-06-01 17:51:10] INFO - super_gradients.training.utils.sg_trainer_utils - TRAINING PARAMETERS:
    - Mode:                         Single GPU
    - Number of GPUs:               0          (0 available on the machine)
    - Full dataset size:            28696      (len(train_set))
    - Batch size per GPU:           16         (batch_size)
    - Batch Accumulate:             1          (batch_accumulate)
    - Total batch size:             16         (num_gpus * batch_size)
    - Effective Batch size:         16         (num_gpus * batch_size * batch_accumulate)
    - Iterations per epoch:         1793       (len(train_loader))
    - Gradient updates per epoch:   1793       (len(train_loader) / batch_accumulate)
    - Model: YoloNAS_S  (19.02M parameters, 19.02M optimized)
    - Learning Rates and Weight Decays:
      - default: (19.02M parameters). LR: 0.0001 (19.02M parameters) WD: 0.0, (42.13K parameters), WD: 0.0005, (18.98M parameters)

[2025-06-01 17:51:10] INFO - super_gradients.training.sg_trainer.sg_trainer - Started training for 100 epochs (0/99)

[2025-06-01 18:41:44] INFO - super_gradients.training.sg_trainer.sg_trainer - 
[MODEL TRAINING EXECUTION HAS BEEN INTERRUPTED]... Please wait until SOFT-TERMINATION process finishes and saves all of the Model Checkpoints and log files before terminating...
[2025-06-01 18:41:44] INFO - super_gradients.training.sg_trainer.sg_trainer - For HARD Termination - Stop the process again
[2025-06-01 18:41:44] INFO - super_gradients.common.sg_loggers.base_sg_logger - [CLEANUP] - Successfully stopped system monitoring process
