#!/usr/bin/env python3
"""
Quick test of MPS-optimized training setup (2 epochs)
"""

import os
import torch
from datetime import datetime
from super_gradients import init_trainer, Trainer
from super_gradients.training import models
from super_gradients.training.dataloaders.dataloaders import coco_detection_yolo_format_train, coco_detection_yolo_format_val
from super_gradients.training.losses import PPYoloELoss
from super_gradients.training.metrics import DetectionMetrics_050
from super_gradients.training.models.detection_models.pp_yolo_e import PPYoloEPostPredictionCallback

def test_mps_training():
    print("🧪 Testing MPS-Optimized Training Setup (2 epochs)")
    print("=" * 60)
    
    # Setup MPS environment
    os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
    os.environ["PYTORCH_MPS_HIGH_WATERMARK_RATIO"] = "0.0"
    torch.set_num_threads(8)
    
    # Verify MPS
    if torch.backends.mps.is_available():
        device = torch.device("mps")
        torch.mps.empty_cache()
        print("✅ MPS available and configured")
    else:
        device = torch.device("cpu")
        print("⚠️ MPS not available, using CPU")
    
    # Initialize SuperGradients
    init_trainer()
    
    # Set default device
    if device.type == "mps":
        torch.set_default_device(device)
        print(f"✅ Default device set to: {device}")
    
    # Create small dataloaders for testing
    train_dataset_params = {
        'data_dir': '/Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo',
        'images_dir': 'images/train',
        'labels_dir': 'labels/train',
        'classes': ['Barcode', 'QR Code'],
        'input_dim': [640, 640],
        'cache_annotations': False,  # Faster for testing
        'ignore_empty_annotations': False
    }
    
    val_dataset_params = {
        'data_dir': '/Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo',
        'images_dir': 'images/valid',
        'labels_dir': 'labels/valid',
        'classes': ['Barcode', 'QR Code'],
        'input_dim': [640, 640],
        'cache_annotations': False,
        'ignore_empty_annotations': False
    }
    
    # Small batch size for quick test
    train_dataloader = coco_detection_yolo_format_train(
        dataset_params=train_dataset_params,
        dataloader_params={
            'batch_size': 4,
            'num_workers': 2,
            'drop_last': True,
            'pin_memory': False,
            'collate_fn': 'DetectionCollateFN'
        }
    )
    
    val_dataloader = coco_detection_yolo_format_val(
        dataset_params=val_dataset_params,
        dataloader_params={
            'batch_size': 4,
            'num_workers': 2,
            'drop_last': False,
            'pin_memory': False,
            'collate_fn': 'DetectionCollateFN'
        }
    )
    
    print("✅ Dataloaders created")
    
    # Load model with transfer learning
    print("🔄 Loading model with transfer learning...")
    model_pretrained = models.get('yolo_nas_s', num_classes=80, pretrained_weights='coco')
    model = models.get('yolo_nas_s', num_classes=2)
    
    # Transfer weights
    pretrained_state_dict = model_pretrained.state_dict()
    model_state_dict = model.state_dict()
    
    transferred_weights = {}
    for name, param in pretrained_state_dict.items():
        if name in model_state_dict and param.shape == model_state_dict[name].shape:
            transferred_weights[name] = param
    
    model.load_state_dict(transferred_weights, strict=False)
    print(f"✅ Transferred {len(transferred_weights)} layers")
    
    # Move model to device
    model = model.to(device)
    print(f"✅ Model on device: {next(model.parameters()).device}")
    
    # Quick training parameters
    training_params = {
        'max_epochs': 2,  # Just 2 epochs for testing
        'lr_mode': 'CosineLRScheduler',
        'initial_lr': 1e-4,
        'optimizer': 'AdamW',
        'loss': PPYoloELoss(num_classes=2),
        'valid_metrics_list': [
            DetectionMetrics_050(
                score_thres=0.1,
                top_k_predictions=300,
                num_cls=2,
                normalize_targets=True,
                post_prediction_callback=PPYoloEPostPredictionCallback(
                    score_threshold=0.01,
                    nms_threshold=0.7,
                    nms_top_k=1000,
                    max_predictions=300
                )
            )
        ],
        'metric_to_watch': 'mAP@0.50',
        'save_model': False,  # Don't save for test
        'mixed_precision': True
    }
    
    # Create trainer
    trainer = Trainer(
        experiment_name='mps_test',
        ckpt_root_dir='test_checkpoints'
    )
    
    print("🏃 Starting 2-epoch MPS test training...")
    print(f"📱 Device: {device}")
    
    try:
        trainer.train(
            model=model,
            training_params=training_params,
            train_loader=train_dataloader,
            valid_loader=val_dataloader
        )
        
        print("🎉 MPS training test completed successfully!")
        print("✅ Your setup is ready for full 100-epoch training!")
        
        # Check if model is still on MPS after training
        final_device = next(model.parameters()).device
        print(f"✅ Model final device: {final_device}")
        
        return True
        
    except Exception as e:
        print(f"❌ MPS training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_mps_training()
    if success:
        print("\n🚀 Ready to run full training with:")
        print("uv run python train_barcode_mps_optimized.py")
    else:
        print("\n❌ Please check the error above before proceeding.")
