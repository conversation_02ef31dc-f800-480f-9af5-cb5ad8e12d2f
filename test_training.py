#!/usr/bin/env python3
"""
Quick test to verify training setup works
"""

import torch
from super_gradients.training import models

def test_model_loading():
    print("🧪 Testing YOLO-NAS model loading with transfer learning...")
    
    try:
        # Test 1: Load pretrained model
        print("Step 1: Loading pretrained COCO model...")
        model_pretrained = models.get('yolo_nas_s', num_classes=80, pretrained_weights='coco')
        print("✅ Pretrained model loaded")
        
        # Test 2: Create target model
        print("Step 2: Creating barcode detection model...")
        model = models.get('yolo_nas_s', num_classes=2)
        print("✅ Target model created")
        
        # Test 3: Transfer weights
        print("Step 3: Transferring weights...")
        pretrained_state_dict = model_pretrained.state_dict()
        model_state_dict = model.state_dict()
        
        transferred_weights = {}
        for name, param in pretrained_state_dict.items():
            if name in model_state_dict and param.shape == model_state_dict[name].shape:
                transferred_weights[name] = param
        
        model.load_state_dict(transferred_weights, strict=False)
        print(f"✅ Transferred {len(transferred_weights)} layers")
        
        # Test 4: MPS compatibility
        if torch.backends.mps.is_available():
            print("Step 4: Testing MPS compatibility...")
            model = model.to('mps')
            
            # Test forward pass
            dummy_input = torch.randn(1, 3, 640, 640).to('mps')
            with torch.no_grad():
                output = model(dummy_input)
            print("✅ MPS forward pass successful")
            print(f"✅ Output shape: {[o.shape for o in output]}")
        
        print("\n🎉 All tests passed! Ready for training!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_model_loading()
