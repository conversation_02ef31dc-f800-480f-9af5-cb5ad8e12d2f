--------- config parameters ----------
{
    "checkpoint_params": {
        "load_checkpoint": false,
        "schema": null
    },
    "training_hyperparams": {
        "lr_warmup_epochs": 0,
        "lr_warmup_steps": 0,
        "lr_cooldown_epochs": 0,
        "warmup_initial_lr": null,
        "cosine_final_lr_ratio": 0.01,
        "optimizer": "AdamW",
        "optimizer_params": {},
        "criterion_params": {},
        "ema": false,
        "batch_accumulate": 1,
        "ema_params": {},
        "zero_weight_decay_on_bias_and_bn": false,
        "load_opt_params": true,
        "run_validation_freq": 1,
        "run_test_freq": 1,
        "save_model": false,
        "metric_to_watch": "mAP@0.50",
        "launch_tensorboard": false,
        "tb_files_user_prompt": false,
        "silent_mode": false,
        "mixed_precision": true,
        "tensorboard_port": null,
        "save_ckpt_epoch_list": [],
        "average_best_models": true,
        "dataset_statistics": false,
        "save_tensorboard_to_s3": false,
        "lr_schedule_function": null,
        "train_metrics_list": [],
        "valid_metrics_list": [
            "DetectionMetrics_050(\n  (post_prediction_callback): PPYoloEPostPredictionCallback()\n)"
        ],
        "greater_metric_to_watch_is_better": true,
        "precise_bn": false,
        "precise_bn_batch_size": null,
        "seed": 42,
        "lr_mode": "CosineLRScheduler",
        "phase_callbacks": null,
        "log_installed_packages": true,
        "sg_logger": "base_sg_logger",
        "sg_logger_params": {
            "tb_files_user_prompt": false,
            "project_name": "",
            "launch_tensorboard": false,
            "tensorboard_port": null,
            "save_checkpoints_remote": false,
            "save_tensorboard_remote": false,
            "save_logs_remote": false
        },
        "warmup_mode": "LinearEpochLRWarmup",
        "step_lr_update_freq": null,
        "lr_updates": [],
        "initial_lr": 0.0001,
        "clip_grad_norm": null,
        "pre_prediction_callback": null,
        "ckpt_best_name": "ckpt_best.pth",
        "enable_qat": false,
        "resume": false,
        "resume_path": null,
        "ckpt_name": "ckpt_latest.pth",
        "resume_strict_load": false,
        "sync_bn": false,
        "kill_ddp_pgroup_on_end": true,
        "max_train_batches": null,
        "max_valid_batches": null,
        "resume_from_remote_sg_logger": false,
        "torch_compile": false,
        "torch_compile_loss": false,
        "torch_compile_options": {
            "mode": "reduce-overhead",
            "fullgraph": false,
            "dynamic": false,
            "backend": "inductor",
            "options": null,
            "disable": false
        },
        "finetune": false,
        "schema": {
            "type": "object",
            "properties": {
                "max_epochs": {
                    "type": "number",
                    "minimum": 1
                },
                "lr_decay_factor": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 1
                },
                "lr_warmup_epochs": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 10
                },
                "initial_lr": {
                    "anyOf": [
                        {
                            "type": [
                                "number",
                                "string",
                                "boolean",
                                "null"
                            ]
                        },
                        {
                            "type": "object",
                            "patternProperties": {
                                "^[a-zA-Z0-9_.]+$": {
                                    "type": "number"
                                }
                            },
                            "additionalProperties": false
                        }
                    ]
                }
            },
            "if": {
                "properties": {
                    "lr_mode": {
                        "const": "StepLRScheduler"
                    }
                }
            },
            "then": {
                "required": [
                    "lr_updates",
                    "lr_decay_factor"
                ]
            },
            "required": [
                "max_epochs",
                "lr_mode",
                "initial_lr",
                "loss"
            ]
        },
        "max_epochs": 5,
        "loss": "PPYoloELoss(\n  (static_assigner): ATSSAssigner()\n  (assigner): TaskAlignedAssigner()\n)"
    },
    "dataset_params": {
        "train_dataset_params": "{'data_dir': '/Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo', 'images_dir': 'images/train', 'labels_dir': 'labels/train', 'classes': ['Barcode', 'QR Code'], 'input_dim': [640, 640], 'cache_annotations': True, 'ignore_empty_annotations': False, 'transforms': [{'DetectionMosaic': {'input_dim': [640, 640], 'prob': 1.0}}, {'DetectionRandomAffine': {'degrees': 10.0, 'translate': 0.1, 'scales': [0.1, 2], 'shear': 2.0, 'target_size': [640, 640], 'filter_box_candidates': True, 'wh_thr': 2, 'area_thr': 0.1, 'ar_thr': 20}}, {'DetectionMixup': {'input_dim': [640, 640], 'mixup_scale': [0.5, 1.5], 'prob': 1.0, 'flip_prob': 0.5}}, {'DetectionHSV': {'prob': 1.0, 'hgain': 5, 'sgain': 30, 'vgain': 30}}, {'DetectionHorizontalFlip': {'prob': 0.5}}, {'DetectionPaddedRescale': {'input_dim': [640, 640]}}, {'DetectionTargetsFormatTransform': {'input_dim': [640, 640], 'output_format': 'LABEL_CXCYWH'}}], 'class_inclusion_list': None, 'max_num_samples': None}",
        "train_dataloader_params": {
            "batch_size": 4,
            "num_workers": 2,
            "drop_last": true,
            "pin_memory": true,
            "collate_fn": "<super_gradients.training.utils.collate_fn.detection_collate_fn.DetectionCollateFN object at 0x167633ed0>",
            "shuffle": true
        },
        "valid_dataset_params": "{'data_dir': '/Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo', 'images_dir': 'images/valid', 'labels_dir': 'labels/valid', 'classes': ['Barcode', 'QR Code'], 'input_dim': [640, 640], 'cache_annotations': True, 'ignore_empty_annotations': False, 'transforms': [{'DetectionPaddedRescale': {'input_dim': [640, 640]}}, {'DetectionTargetsFormatTransform': {'input_dim': [640, 640], 'output_format': 'LABEL_CXCYWH'}}], 'class_inclusion_list': None, 'max_num_samples': None}",
        "valid_dataloader_params": {
            "batch_size": 4,
            "num_workers": 2,
            "drop_last": false,
            "pin_memory": true,
            "collate_fn": "<super_gradients.training.utils.collate_fn.detection_collate_fn.DetectionCollateFN object at 0x16a1f6210>"
        }
    },
    "additional_log_items": {
        "initial_LR": 0.0001,
        "num_devices": 1,
        "multi_gpu": "None",
        "device_type": "cpu",
        "installed_packages": {
            "deprecated": "1.2.18",
            "gitpython": "3.1.44",
            "markupsafe": "3.0.2",
            "pyyaml": "6.0.2",
            "send2trash": "1.8.3",
            "absl-py": "2.3.0",
            "albucore": "0.0.23",
            "albumentations": "1.4.24",
            "annotated-types": "0.7.0",
            "antlr4-python3-runtime": "4.9.3",
            "anyio": "4.9.0",
            "appnope": "0.1.4",
            "arabic-reshaper": "3.0.0",
            "argon2-cffi": "23.1.0",
            "argon2-cffi-bindings": "21.2.0",
            "arrow": "1.3.0",
            "asn1crypto": "1.5.1",
            "asttokens": "3.0.0",
            "async-lru": "2.0.5",
            "attrs": "25.3.0",
            "babel": "2.17.0",
            "beautifulsoup4": "4.13.4",
            "bleach": "6.2.0",
            "boto3": "1.38.27",
            "botocore": "1.38.27",
            "build": "1.2.2.post1",
            "certifi": "2025.4.26",
            "cffi": "1.17.1",
            "charset-normalizer": "3.4.2",
            "click": "8.2.1",
            "coloredlogs": "15.0.1",
            "comm": "0.2.2",
            "contourpy": "1.3.2",
            "coverage": "5.3.1",
            "cryptography": "45.0.3",
            "cssselect2": "0.8.0",
            "cycler": "0.12.1",
            "data-gradients": "0.3.2",
            "debugpy": "1.8.14",
            "decorator": "5.2.1",
            "defusedxml": "0.7.1",
            "docker-pycreds": "0.4.0",
            "einops": "0.3.2",
            "executing": "2.2.0",
            "fastjsonschema": "2.21.1",
            "filelock": "3.18.0",
            "flatbuffers": "25.2.10",
            "fonttools": "4.58.1",
            "fqdn": "1.5.1",
            "fsspec": "2025.5.1",
            "future": "1.0.0",
            "gitdb": "4.0.12",
            "grpcio": "1.71.0",
            "h11": "0.16.0",
            "html5lib": "1.1",
            "httpcore": "1.0.9",
            "httpx": "0.28.1",
            "humanfriendly": "10.0",
            "hydra-core": "1.3.2",
            "idna": "3.10",
            "imagededup": "0.3.3",
            "imagesize": "1.4.1",
            "ipykernel": "6.29.5",
            "ipython": "9.3.0",
            "ipython-pygments-lexers": "1.1.1",
            "ipywidgets": "8.1.7",
            "isoduration": "20.11.0",
            "jedi": "0.19.2",
            "jinja2": "3.1.6",
            "jmespath": "1.0.1",
            "joblib": "1.5.1",
            "json5": "0.12.0",
            "json-tricks": "3.16.1",
            "jsonpointer": "3.0.0",
            "jsonschema": "4.24.0",
            "jsonschema-specifications": "2025.4.1",
            "jupyter": "1.1.1",
            "jupyter-client": "8.6.3",
            "jupyter-console": "6.6.3",
            "jupyter-core": "5.8.1",
            "jupyter-events": "0.12.0",
            "jupyter-lsp": "2.2.5",
            "jupyter-server": "2.16.0",
            "jupyter-server-terminals": "0.5.3",
            "jupyterlab": "4.4.3",
            "jupyterlab-pygments": "0.3.0",
            "jupyterlab-server": "2.27.3",
            "jupyterlab-widgets": "3.0.15",
            "kiwisolver": "1.4.8",
            "lxml": "5.4.0",
            "markdown": "3.8",
            "markdown-it-py": "3.0.0",
            "matplotlib": "3.10.3",
            "matplotlib-inline": "0.1.7",
            "mdurl": "0.1.2",
            "mistune": "3.1.3",
            "mpmath": "1.3.0",
            "nbclient": "0.10.2",
            "nbconvert": "7.16.6",
            "nbformat": "5.10.4",
            "nest-asyncio": "1.6.0",
            "networkx": "3.5",
            "notebook": "7.4.3",
            "notebook-shim": "0.2.4",
            "numpy": "2.2.6",
            "omegaconf": "2.3.0",
            "onnx": "1.15.0",
            "onnxruntime": "1.22.0",
            "onnxsim": "0.4.36",
            "opencv-python": "*********",
            "opencv-python-headless": "*********",
            "oscrypto": "1.3.0",
            "overrides": "7.7.0",
            "packaging": "25.0",
            "pandas": "2.2.3",
            "pandocfilters": "1.5.1",
            "parso": "0.8.4",
            "pexpect": "4.9.0",
            "pillow": "11.2.1",
            "pip": "25.1.1",
            "pip-tools": "7.4.1",
            "platformdirs": "4.3.8",
            "prometheus-client": "0.22.0",
            "prompt-toolkit": "3.0.51",
            "protobuf": "6.31.1",
            "psutil": "7.0.0",
            "ptyprocess": "0.7.0",
            "pure-eval": "0.2.3",
            "pydeprecate": "0.3.2",
            "pycocotools": "2.0.9",
            "pycparser": "2.22",
            "pydantic": "2.11.5",
            "pydantic-core": "2.33.2",
            "pygments": "2.19.1",
            "pyhanko": "0.29.0",
            "pyhanko-certvalidator": "0.27.0",
            "pyparsing": "3.2.3",
            "pypdf": "5.5.0",
            "pyproject-hooks": "1.2.0",
            "python-bidi": "0.6.6",
            "python-dateutil": "2.9.0.post0",
            "python-json-logger": "3.3.0",
            "pytz": "2025.2",
            "pywavelets": "1.8.0",
            "pyzmq": "26.4.0",
            "rapidfuzz": "3.13.0",
            "referencing": "0.36.2",
            "reportlab": "3.6.13",
            "requests": "2.32.3",
            "rfc3339-validator": "0.1.4",
            "rfc3986-validator": "0.1.1",
            "rich": "14.0.0",
            "rpds-py": "0.25.1",
            "s3transfer": "0.13.0",
            "scikit-learn": "1.6.1",
            "scipy": "1.15.3",
            "seaborn": "0.13.2",
            "sentry-sdk": "2.29.1",
            "setproctitle": "1.3.6",
            "setuptools": "80.9.0",
            "simsimd": "6.2.1",
            "six": "1.17.0",
            "smmap": "5.0.2",
            "sniffio": "1.3.1",
            "soupsieve": "2.7",
            "stack-data": "0.6.3",
            "stringcase": "1.2.0",
            "stringzilla": "3.12.5",
            "svglib": "1.5.1",
            "sympy": "1.14.0",
            "tensorboard": "2.19.0",
            "tensorboard-data-server": "0.7.2",
            "termcolor": "1.1.0",
            "terminado": "0.18.1",
            "threadpoolctl": "3.6.0",
            "tinycss2": "1.4.0",
            "torch": "2.7.0",
            "torchaudio": "2.7.0",
            "torchmetrics": "0.8.0",
            "torchvision": "0.22.0",
            "tornado": "6.5.1",
            "tqdm": "4.67.1",
            "traitlets": "5.14.3",
            "treelib": "1.6.1",
            "types-python-dateutil": "2.9.0.20250516",
            "typing-extensions": "4.13.2",
            "typing-inspection": "0.4.1",
            "tzdata": "2025.2",
            "tzlocal": "5.3.1",
            "uri-template": "1.3.0",
            "uritools": "5.0.0",
            "urllib3": "2.4.0",
            "wandb": "0.19.11",
            "wcwidth": "0.2.13",
            "webcolors": "24.11.1",
            "webencodings": "0.5.1",
            "websocket-client": "1.8.0",
            "werkzeug": "3.1.3",
            "wheel": "0.45.1",
            "widgetsnbextension": "4.0.14",
            "wrapt": "1.17.2",
            "xhtml2pdf": "0.2.11",
            "yolo-nas-barcode-detection": "1.0.0",
            "autocommand": "2.2.2",
            "backports.tarfile": "1.2.0",
            "importlib-metadata": "8.0.0",
            "inflect": "7.3.1",
            "jaraco.collections": "5.1.0",
            "jaraco.context": "5.3.0",
            "jaraco.functools": "4.0.1",
            "jaraco.text": "3.12.1",
            "more-itertools": "10.3.0",
            "tomli": "2.0.1",
            "typeguard": "4.3.0",
            "zipp": "3.19.2"
        }
    }
}
------- config parameters end --------
