============================================================
New run started at 2025-06-01.17:44:57.677743
sys.argv: "quick_train_test.py"
============================================================
The console stream is logged into /Users/<USER>/sg_logs/console.log
/Volumes/DATA/projects/Yolo-nas/src/super_gradients/common/environment/cfg_utils.py:6: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/albumentations/__init__.py:24: UserWarning: A new version of Albumentations is available: 2.0.8 (you have 1.4.24). Upgrade using: pip install -U albumentations. To disable automatic update checks, set the environment variable NO_ALBUMENTATIONS_UPDATE to 1.
  check_for_updates()
🧪 Quick Training Test (5 epochs)
==================================================

Indexing dataset annotations:   0%|                                                                                                                                                                         | 0/28696 [00:00<?, ?it/s]
Indexing dataset annotations:   8%|███████████▊                                                                                                                                               | 2185/28696 [00:00<00:01, 21849.19it/s]
Indexing dataset annotations:  15%|███████████████████████▊                                                                                                                                   | 4400/28696 [00:00<00:01, 22025.34it/s]
Indexing dataset annotations:  23%|███████████████████████████████████▉                                                                                                                       | 6646/28696 [00:00<00:00, 22223.63it/s]
Indexing dataset annotations:  31%|███████████████████████████████████████████████▉                                                                                                           | 8869/28696 [00:00<00:00, 22171.08it/s]
Indexing dataset annotations:  39%|███████████████████████████████████████████████████████████▌                                                                                              | 11103/28696 [00:00<00:00, 22229.56it/s]
Indexing dataset annotations:  46%|███████████████████████████████████████████████████████████████████████▌                                                                                  | 13326/28696 [00:00<00:00, 22162.71it/s]
Indexing dataset annotations:  54%|███████████████████████████████████████████████████████████████████████████████████▍                                                                      | 15556/28696 [00:00<00:00, 22205.87it/s]
Indexing dataset annotations:  62%|███████████████████████████████████████████████████████████████████████████████████████████████▍                                                          | 17790/28696 [00:00<00:00, 22245.22it/s]
Indexing dataset annotations:  70%|███████████████████████████████████████████████████████████████████████████████████████████████████████████▌                                              | 20032/28696 [00:00<00:00, 22298.89it/s]
Indexing dataset annotations:  78%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▍                                  | 22262/28696 [00:01<00:00, 22281.64it/s]
Indexing dataset annotations:  85%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▍                      | 24491/28696 [00:01<00:00, 22232.20it/s]
Indexing dataset annotations:  93%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▍          | 26719/28696 [00:01<00:00, 22243.80it/s]
Indexing dataset annotations: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 28696/28696 [00:01<00:00, 22216.70it/s]

Indexing dataset annotations:   0%|                                                                                                                                                                          | 0/2382 [00:00<?, ?it/s]
Indexing dataset annotations:  94%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▏         | 2233/2382 [00:00<00:00, 22325.76it/s]
Indexing dataset annotations: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 2382/2382 [00:00<00:00, 22276.25it/s]
🔄 Setting up model...
✅ Transferred 915 layers
🏃 Starting 5-epoch test training...
The console stream is now moved to test_checkpoints/quick_test/RUN_20250601_174501_407238/console_Jun01_17_45_01.txt
/Volumes/DATA/projects/Yolo-nas/src/super_gradients/training/sg_trainer/sg_trainer.py:1761: UserWarning: Mixed precision training is not supported on CPU. Disabling mixed precision. (i.e. `mixed_precision=False`)
  warnings.warn("Mixed precision training is not supported on CPU. Disabling mixed precision. (i.e. `mixed_precision=False`)")
/Volumes/DATA/projects/Yolo-nas/src/super_gradients/training/sg_trainer/sg_trainer.py:1765: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  self.scaler = GradScaler(enabled=mixed_precision_enabled)
[2025-06-01 17:45:01] WARNING - sg_trainer.py - 'training_params.average_best_models'  is enabled, but 'training_params.save_model' is disabled. 
Model averaging requires saving snapshot checkpoints to function properly. As a result, 'training_params.average_best_models' will be disabled. 
/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py:683: UserWarning: 'pin_memory' argument is set as true but not supported on MPS now, then device pinned memory won't be used.
  warnings.warn(warn_msg)
[2025-06-01 17:45:23] INFO - sg_trainer_utils.py - TRAINING PARAMETERS:
    - Mode:                         Single GPU
    - Number of GPUs:               0          (0 available on the machine)
    - Full dataset size:            28696      (len(train_set))
    - Batch size per GPU:           4          (batch_size)
    - Batch Accumulate:             1          (batch_accumulate)
    - Total batch size:             4          (num_gpus * batch_size)
    - Effective Batch size:         4          (num_gpus * batch_size * batch_accumulate)
    - Iterations per epoch:         7174       (len(train_loader))
    - Gradient updates per epoch:   7174       (len(train_loader) / batch_accumulate)
    - Model: YoloNAS_S  (19.02M parameters, 19.02M optimized)
    - Learning Rates and Weight Decays:
      - default: (19.02M parameters). LR: 0.0001 (19.02M parameters) WD: 0.01, (19.02M parameters)

[2025-06-01 17:45:23] INFO - sg_trainer.py - Started training for 5 epochs (0/4)


  0%|          | 0/7174 [00:00<?, ?it/s]
Train epoch 0:   0%|          | 0/7174 [00:00<?, ?it/s]/Volumes/DATA/projects/Yolo-nas/src/super_gradients/training/sg_trainer/sg_trainer.py:503: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast(enabled=self.training_params.mixed_precision):

Train epoch 0:   0%|          | 0/7174 [00:08<?, ?it/s, PPYoloELoss/loss=8.34, PPYoloELoss/loss_cls=5.71, PPYoloELoss/loss_dfl=1.28, PPYoloELoss/loss_iou=1.36, gpu_mem=0]
Train epoch 0:   0%|          | 1/7174 [00:08<17:06:31,  8.59s/it, PPYoloELoss/loss=8.34, PPYoloELoss/loss_cls=5.71, PPYoloELoss/loss_dfl=1.28, PPYoloELoss/loss_iou=1.36, gpu_mem=0]
Train epoch 0:   0%|          | 1/7174 [00:10<17:06:31,  8.59s/it, PPYoloELoss/loss=7.34, PPYoloELoss/loss_cls=5.01, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.2, gpu_mem=0] 
Train epoch 0:   0%|          | 2/7174 [00:10<9:32:00,  4.79s/it, PPYoloELoss/loss=7.34, PPYoloELoss/loss_cls=5.01, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.2, gpu_mem=0] 
Train epoch 0:   0%|          | 2/7174 [00:12<9:32:00,  4.79s/it, PPYoloELoss/loss=6.45, PPYoloELoss/loss_cls=4.01, PPYoloELoss/loss_dfl=1.19, PPYoloELoss/loss_iou=1.25, gpu_mem=0]
Train epoch 0:   0%|          | 3/7174 [00:12<7:06:45,  3.57s/it, PPYoloELoss/loss=6.45, PPYoloELoss/loss_cls=4.01, PPYoloELoss/loss_dfl=1.19, PPYoloELoss/loss_iou=1.25, gpu_mem=0]
Train epoch 0:   0%|          | 3/7174 [00:14<7:06:45,  3.57s/it, PPYoloELoss/loss=6.07, PPYoloELoss/loss_cls=3.75, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.2, gpu_mem=0] 
Train epoch 0:   0%|          | 4/7174 [00:14<5:59:48,  3.01s/it, PPYoloELoss/loss=6.07, PPYoloELoss/loss_cls=3.75, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.2, gpu_mem=0]
Train epoch 0:   0%|          | 4/7174 [00:17<5:59:48,  3.01s/it, PPYoloELoss/loss=5.68, PPYoloELoss/loss_cls=3.36, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   0%|          | 5/7174 [00:17<5:22:32,  2.70s/it, PPYoloELoss/loss=5.68, PPYoloELoss/loss_cls=3.36, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.19, gpu_mem=0]
Train epoch 0:   0%|          | 5/7174 [00:19<5:22:32,  2.70s/it, PPYoloELoss/loss=5.46, PPYoloELoss/loss_cls=3.14, PPYoloELoss/loss_dfl=1.15, PPYoloELoss/loss_iou=1.18, gpu_mem=0]
Train epoch 0:   0%|          | 6/7174 [00:19<4:59:16,  2.51s/it, PPYoloELoss/loss=5.46, PPYoloELoss/loss_cls=3.14, PPYoloELoss/loss_dfl=1.15, PPYoloELoss/loss_iou=1.18, gpu_mem=0]
Train epoch 0:   0%|          | 6/7174 [00:21<4:59:16,  2.51s/it, PPYoloELoss/loss=5.26, PPYoloELoss/loss_cls=2.98, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.15, gpu_mem=0]
Train epoch 0:   0%|          | 7/7174 [00:21<4:45:27,  2.39s/it, PPYoloELoss/loss=5.26, PPYoloELoss/loss_cls=2.98, PPYoloELoss/loss_dfl=1.13, PPYoloELoss/loss_iou=1.15, gpu_mem=0]
Train epoch 0:   0%|          | 7/7174 [00:23<4:45:27,  2.39s/it, PPYoloELoss/loss=5.13, PPYoloELoss/loss_cls=2.87, PPYoloELoss/loss_dfl=1.12, PPYoloELoss/loss_iou=1.14, gpu_mem=0]
Train epoch 0:   0%|          | 8/7174 [00:23<4:37:34,  2.32s/it, PPYoloELoss/loss=5.13, PPYoloELoss/loss_cls=2.87, PPYoloELoss/loss_dfl=1.12, PPYoloELoss/loss_iou=1.14, gpu_mem=0]
Train epoch 0:   0%|          | 8/7174 [00:25<4:37:34,  2.32s/it, PPYoloELoss/loss=4.96, PPYoloELoss/loss_cls=2.75, PPYoloELoss/loss_dfl=1.1, PPYoloELoss/loss_iou=1.12, gpu_mem=0] 
Train epoch 0:   0%|          | 9/7174 [00:25<4:33:07,  2.29s/it, PPYoloELoss/loss=4.96, PPYoloELoss/loss_cls=2.75, PPYoloELoss/loss_dfl=1.1, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   0%|          | 9/7174 [00:27<4:33:07,  2.29s/it, PPYoloELoss/loss=4.85, PPYoloELoss/loss_cls=2.66, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   0%|          | 10/7174 [00:27<4:28:31,  2.25s/it, PPYoloELoss/loss=4.85, PPYoloELoss/loss_cls=2.66, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   0%|          | 10/7174 [00:30<4:28:31,  2.25s/it, PPYoloELoss/loss=4.77, PPYoloELoss/loss_cls=2.58, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   0%|          | 11/7174 [00:30<4:24:38,  2.22s/it, PPYoloELoss/loss=4.77, PPYoloELoss/loss_cls=2.58, PPYoloELoss/loss_dfl=1.08, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   0%|          | 11/7174 [00:32<4:24:38,  2.22s/it, PPYoloELoss/loss=4.68, PPYoloELoss/loss_cls=2.51, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   0%|          | 12/7174 [00:32<4:20:31,  2.18s/it, PPYoloELoss/loss=4.68, PPYoloELoss/loss_cls=2.51, PPYoloELoss/loss_dfl=1.07, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   0%|          | 12/7174 [00:34<4:20:31,  2.18s/it, PPYoloELoss/loss=4.62, PPYoloELoss/loss_cls=2.46, PPYoloELoss/loss_dfl=1.06, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   0%|          | 13/7174 [00:34<4:20:11,  2.18s/it, PPYoloELoss/loss=4.62, PPYoloELoss/loss_cls=2.46, PPYoloELoss/loss_dfl=1.06, PPYoloELoss/loss_iou=1.11, gpu_mem=0]
Train epoch 0:   0%|          | 13/7174 [00:36<4:20:11,  2.18s/it, PPYoloELoss/loss=4.57, PPYoloELoss/loss_cls=2.42, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.1, gpu_mem=0] 
Train epoch 0:   0%|          | 14/7174 [00:36<4:18:45,  2.17s/it, PPYoloELoss/loss=4.57, PPYoloELoss/loss_cls=2.42, PPYoloELoss/loss_dfl=1.05, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   0%|          | 14/7174 [00:38<4:18:45,  2.17s/it, PPYoloELoss/loss=4.51, PPYoloELoss/loss_cls=2.38, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   0%|          | 15/7174 [00:38<4:17:59,  2.16s/it, PPYoloELoss/loss=4.51, PPYoloELoss/loss_cls=2.38, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.1, gpu_mem=0]
Train epoch 0:   0%|          | 15/7174 [00:40<4:17:59,  2.16s/it, PPYoloELoss/loss=4.51, PPYoloELoss/loss_cls=2.34, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   0%|          | 16/7174 [00:40<4:16:43,  2.15s/it, PPYoloELoss/loss=4.51, PPYoloELoss/loss_cls=2.34, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   0%|          | 16/7174 [00:42<4:16:43,  2.15s/it, PPYoloELoss/loss=4.47, PPYoloELoss/loss_cls=2.31, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
Train epoch 0:   0%|          | 17/7174 [00:42<4:16:38,  2.15s/it, PPYoloELoss/loss=4.47, PPYoloELoss/loss_cls=2.31, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.12, gpu_mem=0]Exception ignored in: <function _MultiProcessingDataLoaderIter.__del__ at 0x1078b0720>
Traceback (most recent call last):
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 1663, in __del__
    self._shutdown_workers()
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 1627, in _shutdown_workers
    w.join(timeout=_utils.MP_STATUS_CHECK_INTERVAL)
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/process.py", line 149, in join
    res = self._popen.wait(timeout)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/popen_fork.py", line 40, in wait
    if not wait([self.sentinel], timeout):
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/multiprocessing/connection.py", line 948, in wait
    ready = selector.select(timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.local/share/uv/python/cpython-3.11.11-macos-aarch64-none/lib/python3.11/selectors.py", line 415, in select
    fd_event_list = self._selector.poll(timeout)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt: 

Train epoch 0:   0%|          | 17/7174 [00:45<5:22:45,  2.71s/it, PPYoloELoss/loss=4.47, PPYoloELoss/loss_cls=2.31, PPYoloELoss/loss_dfl=1.04, PPYoloELoss/loss_iou=1.12, gpu_mem=0]
[2025-06-01 17:46:09] INFO - sg_trainer.py - 
[MODEL TRAINING EXECUTION HAS BEEN INTERRUPTED]... Please wait until SOFT-TERMINATION process finishes and saves all of the Model Checkpoints and log files before terminating...
[2025-06-01 17:46:09] INFO - sg_trainer.py - For HARD Termination - Stop the process again
[2025-06-01 17:46:09] INFO - base_sg_logger.py - [CLEANUP] - Successfully stopped system monitoring process
🎉 Quick test completed successfully!
✅ Your setup is ready for full training!
