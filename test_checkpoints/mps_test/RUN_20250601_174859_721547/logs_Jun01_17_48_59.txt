[2025-06-01 17:48:56] INFO - super_gradients.common.crash_handler.crash_tips_setup - Crash tips is enabled. You can set your environment variable to CRASH_HANDLER=FALSE to disable it
[2025-06-01 17:48:56] DEBUG - matplotlib - matplotlib data path: /Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/matplotlib/mpl-data
[2025-06-01 17:48:56] DEBUG - matplotlib - CONFIGDIR=/Users/<USER>/.matplotlib
[2025-06-01 17:48:56] DEBUG - matplotlib - interactive is False
[2025-06-01 17:48:56] DEBUG - matplotlib - platform is darwin
[2025-06-01 17:48:56] DEBUG - matplotlib - CACHEDIR=/Users/<USER>/.matplotlib
[2025-06-01 17:48:56] DEBUG - matplotlib.font_manager - Using fontManager instance from /Users/<USER>/.matplotlib/fontlist-v390.json
[2025-06-01 17:48:56] DEBUG - super_gradients.common.sg_loggers.clearml_sg_logger - Failed to import clearml
[2025-06-01 17:48:57] DEBUG - git.cmd - Popen(['git', 'version'], cwd=/Volumes/DATA/projects/Yolo-nas, stdin=None, shell=False, universal_newlines=False)
[2025-06-01 17:48:57] DEBUG - git.cmd - Popen(['git', 'version'], cwd=/Volumes/DATA/projects/Yolo-nas, stdin=None, shell=False, universal_newlines=False)
[2025-06-01 17:48:57] DEBUG - wandb.docker.auth - Trying paths: ['/Users/<USER>/.docker/config.json', '/Users/<USER>/.dockercfg']
[2025-06-01 17:48:57] DEBUG - wandb.docker.auth - Found file at path: /Users/<USER>/.docker/config.json
[2025-06-01 17:48:57] DEBUG - wandb.docker.auth - Found 'credsStore' section
[2025-06-01 17:48:57] DEBUG - hydra.core.utils - Setting JobRuntime:name=UNKNOWN_NAME
[2025-06-01 17:48:57] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:48:57] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:48:58] WARNING - super_gradients.sanity_check.env_sanity_check - [31mFailed to verify operating system: Deci officially supports only Linux kernels. Some features may not work as expected.[0m
[2025-06-01 17:48:58] DEBUG - super_gradients.sanity_check.env_sanity_check - setuptools==80.9.0 does not satisfy requirement setuptools<67.0.0,>=65.5.1
[2025-06-01 17:48:58] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:48:58] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:48:58] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:48:58] WARNING - super_gradients.training.utils.checkpoint_utils - :warning: The pre-trained models provided by SuperGradients may have their own licenses or terms and conditions derived from the dataset used for pre-training.
 It is your responsibility to determine whether you have permission to use the models for your use case.
 The model you have requested was pre-trained on the coco dataset, published under the following terms: https://cocodataset.org/#termsofuse
[2025-06-01 17:48:58] INFO - super_gradients.training.utils.checkpoint_utils - License Notification: YOLO-NAS pre-trained weights are subjected to the specific license terms and conditions detailed in 
https://github.com/Deci-AI/super-gradients/blob/master/LICENSE.YOLONAS.md
By downloading the pre-trained weight files you agree to comply with these terms.
[2025-06-01 17:48:59] INFO - super_gradients.training.utils.checkpoint_utils - Successfully loaded pretrained weights for architecture yolo_nas_s
[2025-06-01 17:48:59] DEBUG - super_gradients.training.utils.checkpoint_utils - Trying to load preprocessing params from checkpoint. Preprocessing params in checkpoint: False. Model YoloNAS_S inherit HasPredict: True
[2025-06-01 17:48:59] DEBUG - hydra.core.utils - Setting JobRuntime:name=app
[2025-06-01 17:48:59] INFO - super_gradients.training.sg_trainer.sg_trainer - Starting a new run with `run_id=RUN_20250601_174859_721547`
[2025-06-01 17:48:59] INFO - super_gradients.training.sg_trainer.sg_trainer - Checkpoints directory: test_checkpoints/mps_test/RUN_20250601_174859_721547
[2025-06-01 17:48:59] WARNING - super_gradients.training.sg_trainer.sg_trainer - 'training_params.average_best_models'  is enabled, but 'training_params.save_model' is disabled. 
Model averaging requires saving snapshot checkpoints to function properly. As a result, 'training_params.average_best_models' will be disabled. 
