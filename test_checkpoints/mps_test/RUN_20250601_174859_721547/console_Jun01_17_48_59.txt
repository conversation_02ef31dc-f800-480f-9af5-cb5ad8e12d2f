============================================================
New run started at 2025-06-01.17:48:56.193092
sys.argv: "test_mps_training.py"
============================================================
The console stream is logged into /Users/<USER>/sg_logs/console.log
/Volumes/DATA/projects/Yolo-nas/src/super_gradients/common/environment/cfg_utils.py:6: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/albumentations/__init__.py:24: UserWarning: A new version of Albumentations is available: 2.0.8 (you have 1.4.24). Upgrade using: pip install -U albumentations. To disable automatic update checks, set the environment variable NO_ALBUMENTATIONS_UPDATE to 1.
  check_for_updates()
🧪 Testing MPS-Optimized Training Setup (2 epochs)
============================================================
✅ MPS available and configured
✅ Default device set to: mps
✅ Dataloaders created
🔄 Loading model with transfer learning...
✅ Transferred 915 layers
✅ Model on device: mps:0
🏃 Starting 2-epoch MPS test training...
📱 Device: mps
The console stream is now moved to test_checkpoints/mps_test/RUN_20250601_174859_721547/console_Jun01_17_48_59.txt
/Volumes/DATA/projects/Yolo-nas/src/super_gradients/training/sg_trainer/sg_trainer.py:1761: UserWarning: Mixed precision training is not supported on CPU. Disabling mixed precision. (i.e. `mixed_precision=False`)
  warnings.warn("Mixed precision training is not supported on CPU. Disabling mixed precision. (i.e. `mixed_precision=False`)")
/Volumes/DATA/projects/Yolo-nas/src/super_gradients/training/sg_trainer/sg_trainer.py:1765: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  self.scaler = GradScaler(enabled=mixed_precision_enabled)
[2025-06-01 17:48:59] WARNING - sg_trainer.py - 'training_params.average_best_models'  is enabled, but 'training_params.save_model' is disabled. 
Model averaging requires saving snapshot checkpoints to function properly. As a result, 'training_params.average_best_models' will be disabled. 
❌ MPS training test failed: Expected a 'mps:0' generator device but found 'cpu'
Traceback (most recent call last):
  File "/Volumes/DATA/projects/Yolo-nas/test_mps_training.py", line 145, in test_mps_training
    trainer.train(
  File "/Volumes/DATA/projects/Yolo-nas/src/super_gradients/training/sg_trainer/sg_trainer.py", line 1492, in train
    first_train_batch = next(iter(self.train_loader))
                             ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 493, in __iter__
    return self._get_iterator()
           ^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 424, in _get_iterator
    return _MultiProcessingDataLoaderIter(self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 1227, in __init__
    self._reset(loader, first_iter=True)
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 1269, in _reset
    self._try_put_index()
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 1522, in _try_put_index
    index = self._next_index()
            ^^^^^^^^^^^^^^^^^^
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/dataloader.py", line 723, in _next_index
    return next(self._sampler_iter)  # may raise StopIteration
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/sampler.py", line 332, in __iter__
    for batch_droplast in zip(*args):
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/data/sampler.py", line 189, in __iter__
    yield from torch.randperm(n, generator=generator).tolist()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/DATA/projects/Yolo-nas/.venv/lib/python3.11/site-packages/torch/utils/_device.py", line 104, in __torch_function__
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
RuntimeError: Expected a 'mps:0' generator device but found 'cpu'

❌ Please check the error above before proceeding.
