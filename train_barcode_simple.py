#!/usr/bin/env python3
"""
Simple YOLO-NAS Barcode Detection Training Script
Uses SuperGradients' proven training pipeline with transfer learning
"""

import os
import torch
from super_gradients import init_trainer, Trainer
from super_gradients.training import models
from super_gradients.training.dataloaders.dataloaders import (
    coco_detection_yolo_format_train, 
    coco_detection_yolo_format_val
)
from super_gradients.training.losses import PPYoloELoss
from super_gradients.training.metrics import DetectionMetrics_050
from super_gradients.training.models.detection_models.pp_yolo_e import PPYoloEPostPredictionCallback

def main():
    print("🚀 Starting YOLO-NAS Barcode Detection Training")
    print("=" * 60)
    
    # Initialize trainer
    init_trainer()
    
    # Check MPS availability
    if torch.backends.mps.is_available():
        print("✅ MPS Available - Using Apple Silicon optimization")
        device = "mps"
    else:
        print("⚠️ MPS Not Available - Using CPU")
        device = "cpu"
    
    # Dataset parameters
    dataset_params = {
        'data_dir': '/Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo',
        'train_images_dir': 'images/train',
        'train_labels_dir': 'labels/train',
        'val_images_dir': 'images/valid', 
        'val_labels_dir': 'labels/valid',
        'classes': ['Barcode', 'QR Code']
    }
    
    # Create dataloaders
    print("📊 Creating dataloaders...")
    train_dataloader = coco_detection_yolo_format_train(
        dataset_params=dataset_params,
        dataloader_params={
            'batch_size': 16,  # Adjust based on your memory
            'num_workers': 4,
            'drop_last': True,
            'pin_memory': True,
            'collate_fn': 'DetectionCollateFN'
        }
    )
    
    val_dataloader = coco_detection_yolo_format_val(
        dataset_params=dataset_params,
        dataloader_params={
            'batch_size': 16,
            'num_workers': 4,
            'drop_last': False,
            'pin_memory': True,
            'collate_fn': 'DetectionCollateFN'
        }
    )
    
    # Load model with transfer learning
    print("🔄 Loading YOLO-NAS-S with transfer learning...")
    
    # Step 1: Load pretrained model
    model_pretrained = models.get('yolo_nas_s', num_classes=80, pretrained_weights='coco')
    
    # Step 2: Create target model
    model = models.get('yolo_nas_s', num_classes=2)
    
    # Step 3: Transfer compatible weights
    pretrained_state_dict = model_pretrained.state_dict()
    model_state_dict = model.state_dict()
    
    transferred_weights = {}
    for name, param in pretrained_state_dict.items():
        if name in model_state_dict and param.shape == model_state_dict[name].shape:
            transferred_weights[name] = param
    
    model.load_state_dict(transferred_weights, strict=False)
    print(f"✅ Transferred {len(transferred_weights)} layers from COCO pretrained model")
    
    # Move model to device
    model = model.to(device)
    print(f"📱 Model moved to {device}")
    
    # Training parameters
    training_params = {
        'max_epochs': 100,
        'lr_mode': 'CosineLRScheduler',
        'initial_lr': 1e-4,  # Lower LR for transfer learning
        'lr_warmup_epochs': 3,
        'optimizer': 'AdamW',
        'optimizer_params': {'weight_decay': 0.0005},
        'ema': True,
        'ema_params': {'decay': 0.9999},
        'loss': PPYoloELoss(),
        'valid_metrics_list': [
            DetectionMetrics_050(
                score_thres=0.1,
                top_k_predictions=300,
                num_cls=2,
                normalize_targets=True,
                post_prediction_callback=PPYoloEPostPredictionCallback(
                    score_threshold=0.01,
                    nms_threshold=0.7,
                    nms_top_k=1000,
                    max_predictions=300
                )
            )
        ],
        'metric_to_watch': 'mAP@0.50',
        'greater_metric_to_watch_is_better': True,
        'save_model': True,
        'save_ckpt_epoch_list': [25, 50, 75],
        'mixed_precision': True,
        'sync_bn': False,
        'zero_weight_decay_on_bias_and_bn': True,
        'average_best_models': True
    }
    
    # Create trainer
    trainer = Trainer(
        experiment_name='yolo_nas_s_barcode_detection',
        ckpt_root_dir='checkpoints'
    )
    
    # Start training
    print("🏃 Starting training...")
    trainer.train(
        model=model,
        training_params=training_params,
        train_loader=train_dataloader,
        valid_loader=val_dataloader
    )
    
    print("🎉 Training completed!")
    print("📊 Check results in: checkpoints/yolo_nas_s_barcode_detection/")

if __name__ == "__main__":
    main()
