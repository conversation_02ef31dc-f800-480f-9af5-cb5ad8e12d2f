# YOLO-NAS Barcode Detection Project
[project]
name = "yolo-nas-barcode-detection"
version = "1.0.0"
description = "YOLO-NAS barcode detection training optimized for Apple Silicon"
readme = "README_APPLE_SILICON.md"
requires-python = ">=3.11"
dependencies = [
    "albumentations~=1.3",
    "boto3>=1.38.27",
    "data-gradients~=0.3.1",
    "deprecated>=1.2.18",
    "einops==0.3.2",
    "hydra-core>=1.2.0",
    "imagesize~=1.4.1",
    "ipywidgets>=8.1.7",
    "json-tricks==3.16.1",
    "jsonschema>=4.24.0",
    "jupyter>=1.1.1",
    "matplotlib>=3.3.4",
    "onnx==1.15.0",
    "onnxruntime>=1.15.0",
    "onnxsim>=0.4.3,<1.0",
    "opencv-python>=*********",
    "packaging>=20.4",
    "pillow>=10.2.0",
    "pip-tools>=7.4.1",
    "psutil>=5.8.0",
    "pycocotools>=2.0.9",
    "pyyaml>=6.0.2",
    "rapidfuzz>=3.13.0",
    "scipy>=1.6.1",
    "seaborn>=0.13.2",
    "setuptools>=65.5.1",
    "stringcase>=1.2.0",
    "tensorboard>=2.4.1",
    "termcolor==1.1.0",
    "torch>=2.0.0",
    "torchaudio>=2.0.0",
    "torchmetrics==0.8",
    "torchvision>=0.15.0",
    "tqdm>=4.57.0",
    "treelib==1.6.1",
    "wandb>=0.19.11",
]

[build-system]
requires = ["setuptools>=61", "wheel"]
build-backend = "setuptools.build_meta"

## Super-Gradients configuration for black/mypy/isort etc tools.
[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 160

## Configuration for Black.
[tool.black]
line-length = 160
target-version = ['py36', 'py37', 'py38', 'py39' ]
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.circleci
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.uv.sources]
yolo-nas-barcode-detection = { workspace = true }

[dependency-groups]
dev = [
    "yolo-nas-barcode-detection",
]
