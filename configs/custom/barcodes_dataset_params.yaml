_convert_: all
train_dataloader_params:
  batch_size: 16
  collate_fn: DetectionCollateFN
  drop_last: true
  num_workers: 4
  pin_memory: true
  shuffle: true
train_dataset_params:
  cache_annotations: true
  classes:
  - Barcode
  - QR Code
  data_dir: /Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo
  ignore_empty_annotations: false
  images_dir: images/train
  input_dim:
  - 416
  - 416
  labels_dir: labels/train
  transforms:
  - DetectionMosaic:
      input_dim:
      - 416
      - 416
      prob: 0.8
  - DetectionRandomAffine:
      ar_thr: 30
      area_thr: 0.05
      degrees: 5.0
      filter_box_candidates: true
      scales:
      - 0.8
      - 1.2
      shear: 1.0
      target_size:
      - 416
      - 416
      translate: 0.05
      wh_thr: 2
  - DetectionMixup:
      flip_prob: 0.3
      input_dim:
      - 416
      - 416
      mixup_scale:
      - 0.7
      - 1.3
      prob: 0.3
  - DetectionHSV:
      hgain: 3
      prob: 0.8
      sgain: 15
      vgain: 15
  - DetectionHorizontalFlip:
      prob: 0.3
  - DetectionPaddedRescale:
      input_dim:
      - 416
      - 416
  - DetectionTargetsFormatTransform:
      input_dim:
      - 416
      - 416
      output_format: LABEL_CXCYWH
val_dataloader_params:
  batch_size: 16
  collate_fn: DetectionCollateFN
  drop_last: false
  num_workers: 4
  pin_memory: true
val_dataset_params:
  cache_annotations: true
  classes:
  - Barcode
  - QR Code
  data_dir: /Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo
  ignore_empty_annotations: false
  images_dir: images/valid
  input_dim:
  - 416
  - 416
  labels_dir: labels/valid
  transforms:
  - DetectionPaddedRescale:
      input_dim:
      - 416
      - 416
  - DetectionTargetsFormatTransform:
      input_dim:
      - 416
      - 416
      output_format: LABEL_CXCYWH
