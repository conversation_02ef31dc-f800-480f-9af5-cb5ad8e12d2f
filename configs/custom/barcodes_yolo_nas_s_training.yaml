arch_params:
  num_classes: 2
architecture: yolo_nas_s
checkpoint_params:
  pretrained_weights: coco
dataset_name: barcodes
defaults:
- training_hyperparams: coco2017_yolo_nas_train_params
- dataset_params: custom/barcodes_dataset_params
- checkpoint_params: default_checkpoint_params
- arch_params: yolo_nas_s_arch_params
- _self_
- variable_setup
experiment_name: yolo_nas_s_barcode_detection
experiment_suffix: _barcode_apple_silicon
load_checkpoint: false
multi_gpu: 'Off'
num_classes: 2
num_gpus: 1
result_path: checkpoints/custom/barcodes
resume: false
train_dataloader: yolo_nas_train
training_hyperparams:
  batch_accumulate: 1
  cosine_final_lr_ratio: 0.01
  criterion_params:
    num_classes: 2
    reg_max: 16
  early_stop:
    min_delta: 0.001
    patience: 15
  ema: true
  ema_params:
    decay: 0.9999
  greater_metric_to_watch_is_better: true
  initial_lr: 0.001
  loss: PPYoloELoss
  lr_mode: CosineLRScheduler
  lr_warmup_epochs: 5
  max_epochs: 100
  metric_to_watch: mAP@0.50
  mixed_precision: true
  optimizer: AdamW
  optimizer_params:
    weight_decay: 0.0005
  phase_callbacks:
  - LRCallbackBase:
      freq: 1
      phase: TRAIN_EPOCH_END
  resume: false
  save_ckpt_epoch_list:
  - 10
  - 25
  - 50
  - 75
  save_model: true
  sync_bn: false
  valid_metrics_list:
  - DetectionMetrics_050:
      normalize_targets: true
      num_cls: 2
      post_prediction_callback:
        _target_: super_gradients.training.models.detection_models.pp_yolo_e.PPYoloEPostPredictionCallback
        max_predictions: 300
        nms_threshold: 0.7
        nms_top_k: 1000
        score_threshold: 0.01
      score_thres: 0.1
      top_k_predictions: 300
  - DetectionMetrics_050_095:
      normalize_targets: true
      num_cls: 2
      post_prediction_callback:
        _target_: super_gradients.training.models.detection_models.pp_yolo_e.PPYoloEPostPredictionCallback
        max_predictions: 300
        nms_threshold: 0.7
        nms_top_k: 1000
        score_threshold: 0.01
      score_thres: 0.1
      top_k_predictions: 300
  warmup_mode: LinearEpochLRWarmup
  zero_weight_decay_on_bias_and_bn: true
val_dataloader: yolo_nas_val
