#!/usr/bin/env python3
"""
Quick 5-epoch training test to verify everything works
"""

import torch
from super_gradients import init_trainer, Trainer
from super_gradients.training import models
from super_gradients.training.dataloaders.dataloaders import coco_detection_yolo_format_train, coco_detection_yolo_format_val
from super_gradients.training.losses import PPYoloELoss
from super_gradients.training.metrics import DetectionMetrics_050
from super_gradients.training.models.detection_models.pp_yolo_e import PPYoloEPostPredictionCallback

def quick_training_test():
    print("🧪 Quick Training Test (5 epochs)")
    print("=" * 50)
    
    # Initialize
    init_trainer()
    
    # Dataset parameters for YOLO format
    train_dataset_params = {
        'data_dir': '/Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo',
        'images_dir': 'images/train',
        'labels_dir': 'labels/train',
        'classes': ['Barcode', 'QR Code'],
        'input_dim': [640, 640],
        'cache_annotations': True,
        'ignore_empty_annotations': False
    }

    val_dataset_params = {
        'data_dir': '/Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo',
        'images_dir': 'images/valid',
        'labels_dir': 'labels/valid',
        'classes': ['Barcode', 'QR Code'],
        'input_dim': [640, 640],
        'cache_annotations': True,
        'ignore_empty_annotations': False
    }

    # Small batch size for quick test
    train_dataloader = coco_detection_yolo_format_train(
        dataset_params=train_dataset_params,
        dataloader_params={
            'batch_size': 4,  # Small batch for quick test
            'num_workers': 2,
            'drop_last': True,
            'pin_memory': True,
            'collate_fn': 'DetectionCollateFN'
        }
    )

    val_dataloader = coco_detection_yolo_format_val(
        dataset_params=val_dataset_params,
        dataloader_params={
            'batch_size': 4,
            'num_workers': 2,
            'drop_last': False,
            'pin_memory': True,
            'collate_fn': 'DetectionCollateFN'
        }
    )
    
    # Load model with transfer learning
    print("🔄 Setting up model...")
    model_pretrained = models.get('yolo_nas_s', num_classes=80, pretrained_weights='coco')
    model = models.get('yolo_nas_s', num_classes=2)
    
    # Transfer weights
    pretrained_state_dict = model_pretrained.state_dict()
    model_state_dict = model.state_dict()
    
    transferred_weights = {}
    for name, param in pretrained_state_dict.items():
        if name in model_state_dict and param.shape == model_state_dict[name].shape:
            transferred_weights[name] = param
    
    model.load_state_dict(transferred_weights, strict=False)
    print(f"✅ Transferred {len(transferred_weights)} layers")
    
    # Quick training parameters
    training_params = {
        'max_epochs': 5,  # Just 5 epochs for testing
        'lr_mode': 'CosineLRScheduler',
        'initial_lr': 1e-4,
        'optimizer': 'AdamW',
        'loss': PPYoloELoss(num_classes=2),
        'valid_metrics_list': [
            DetectionMetrics_050(
                score_thres=0.1,
                top_k_predictions=300,
                num_cls=2,
                normalize_targets=True,
                post_prediction_callback=PPYoloEPostPredictionCallback(
                    score_threshold=0.01,
                    nms_threshold=0.7,
                    nms_top_k=1000,
                    max_predictions=300
                )
            )
        ],
        'metric_to_watch': 'mAP@0.50',
        'save_model': False,  # Don't save for quick test
        'mixed_precision': True
    }
    
    # Create trainer
    trainer = Trainer(
        experiment_name='quick_test',
        ckpt_root_dir='test_checkpoints'
    )
    
    print("🏃 Starting 5-epoch test training...")
    trainer.train(
        model=model,
        training_params=training_params,
        train_loader=train_dataloader,
        valid_loader=val_dataloader
    )
    
    print("🎉 Quick test completed successfully!")
    print("✅ Your setup is ready for full training!")

if __name__ == "__main__":
    quick_training_test()
