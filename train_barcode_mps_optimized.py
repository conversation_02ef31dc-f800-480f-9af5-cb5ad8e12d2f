#!/usr/bin/env python3
"""
MPS-Optimized YOLO-NAS Barcode Detection Training Script
Explicitly configured for Apple Silicon with MPS acceleration
"""

import os
import torch
import time
from datetime import datetime
from super_gradients import init_trainer, Trainer
from super_gradients.training import models
from super_gradients.training.dataloaders.dataloaders import coco_detection_yolo_format_train, coco_detection_yolo_format_val
from super_gradients.training.losses import PPYoloELoss
from super_gradients.training.metrics import DetectionMetrics_050, DetectionMetrics_050_095
from super_gradients.training.models.detection_models.pp_yolo_e import PPYoloEPostPredictionCallback
from super_gradients.common.object_names import Models

def setup_mps_environment():
    """Configure environment for optimal MPS performance"""
    print("🔧 Configuring Apple Silicon MPS environment...")
    
    # Set MPS environment variables for optimal performance
    os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
    os.environ["PYTORCH_MPS_HIGH_WATERMARK_RATIO"] = "0.0"
    
    # Set optimal thread count for M4 chip
    torch.set_num_threads(8)
    
    # Clear MPS cache
    if torch.backends.mps.is_available():
        torch.mps.empty_cache()
        print("✅ MPS cache cleared")
    
    print("✅ MPS environment configured")

def verify_mps_setup():
    """Verify MPS is working correctly"""
    print("🔍 Verifying MPS setup...")
    
    if not torch.backends.mps.is_available():
        print("❌ MPS not available! Falling back to CPU.")
        return False, "cpu"
    
    try:
        device = torch.device("mps")
        test_tensor = torch.randn(100, 100).to(device)
        result = torch.mm(test_tensor, test_tensor)
        print(f"✅ MPS verification passed: {result.device}")
        return True, "mps"
    except Exception as e:
        print(f"❌ MPS verification failed: {e}")
        print("Falling back to CPU.")
        return False, "cpu"

def create_mps_optimized_dataloaders(device):
    """Create dataloaders optimized for MPS training"""
    print("📊 Creating MPS-optimized dataloaders...")
    
    # Dataset parameters
    train_dataset_params = {
        'data_dir': '/Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo',
        'images_dir': 'images/train',
        'labels_dir': 'labels/train',
        'classes': ['Barcode', 'QR Code'],
        'input_dim': [640, 640],
        'cache_annotations': True,
        'ignore_empty_annotations': False
    }
    
    val_dataset_params = {
        'data_dir': '/Volumes/DATA/projects/Yolo-nas/datasets/barcodes_yolo',
        'images_dir': 'images/valid',
        'labels_dir': 'labels/valid',
        'classes': ['Barcode', 'QR Code'],
        'input_dim': [640, 640],
        'cache_annotations': True,
        'ignore_empty_annotations': False
    }
    
    # Dataloader parameters optimized for Apple Silicon
    train_dataloader_params = {
        'batch_size': 16,  # Optimal for M4 with 16GB unified memory
        'num_workers': 4,  # Good balance for M4
        'drop_last': True,
        'pin_memory': False,  # Not supported on MPS
        'collate_fn': 'DetectionCollateFN',
        'persistent_workers': True  # Faster data loading
    }
    
    val_dataloader_params = {
        'batch_size': 16,
        'num_workers': 4,
        'drop_last': False,
        'pin_memory': False,
        'collate_fn': 'DetectionCollateFN',
        'persistent_workers': True
    }
    
    # Create dataloaders
    train_dataloader = coco_detection_yolo_format_train(
        dataset_params=train_dataset_params,
        dataloader_params=train_dataloader_params
    )
    
    val_dataloader = coco_detection_yolo_format_val(
        dataset_params=val_dataset_params,
        dataloader_params=val_dataloader_params
    )
    
    print(f"✅ Training dataloader: {len(train_dataloader)} batches")
    print(f"✅ Validation dataloader: {len(val_dataloader)} batches")
    
    return train_dataloader, val_dataloader

def load_model_with_transfer_learning(device):
    """Load YOLO-NAS model with transfer learning and move to MPS"""
    print("🔄 Loading YOLO-NAS-S with transfer learning...")
    
    # Load pretrained model (80 classes)
    print("Loading COCO pretrained model...")
    model_pretrained = models.get('yolo_nas_s', num_classes=80, pretrained_weights='coco')
    
    # Create target model (2 classes)
    print("Creating barcode detection model...")
    model = models.get('yolo_nas_s', num_classes=2)
    
    # Transfer compatible weights
    print("Transferring weights...")
    pretrained_state_dict = model_pretrained.state_dict()
    model_state_dict = model.state_dict()
    
    transferred_weights = {}
    for name, param in pretrained_state_dict.items():
        if name in model_state_dict and param.shape == model_state_dict[name].shape:
            transferred_weights[name] = param
    
    model.load_state_dict(transferred_weights, strict=False)
    print(f"✅ Transferred {len(transferred_weights)} layers from COCO pretrained model")
    
    # Move model to device
    model = model.to(device)
    print(f"✅ Model moved to {device}")
    
    # Verify model is on correct device
    model_device = next(model.parameters()).device
    print(f"✅ Model parameters on: {model_device}")
    
    return model

def create_training_params(device):
    """Create training parameters optimized for MPS"""
    print("⚙️ Creating MPS-optimized training parameters...")
    
    training_params = {
        # Training schedule
        'max_epochs': 100,
        'lr_mode': 'CosineLRScheduler',
        'initial_lr': 1e-4,  # Lower LR for transfer learning
        'lr_warmup_epochs': 5,
        'cosine_final_lr_ratio': 0.01,
        
        # Optimizer
        'optimizer': 'AdamW',
        'optimizer_params': {'weight_decay': 0.0005},
        
        # Model averaging
        'ema': True,
        'ema_params': {'decay': 0.9999},
        
        # Loss function
        'loss': PPYoloELoss(num_classes=2),
        
        # Metrics
        'valid_metrics_list': [
            DetectionMetrics_050(
                score_thres=0.1,
                top_k_predictions=300,
                num_cls=2,
                normalize_targets=True,
                post_prediction_callback=PPYoloEPostPredictionCallback(
                    score_threshold=0.01,
                    nms_threshold=0.7,
                    nms_top_k=1000,
                    max_predictions=300
                )
            ),
            DetectionMetrics_050_095(
                score_thres=0.1,
                top_k_predictions=300,
                num_cls=2,
                normalize_targets=True,
                post_prediction_callback=PPYoloEPostPredictionCallback(
                    score_threshold=0.01,
                    nms_threshold=0.7,
                    nms_top_k=1000,
                    max_predictions=300
                )
            )
        ],
        
        # Monitoring
        'metric_to_watch': 'mAP@0.50',
        'greater_metric_to_watch_is_better': True,
        
        # Checkpointing
        'save_model': True,
        'save_ckpt_epoch_list': [25, 50, 75],
        'average_best_models': True,
        
        # Performance optimizations
        'mixed_precision': True,  # Faster training on Apple Silicon
        'sync_bn': False,
        'zero_weight_decay_on_bias_and_bn': True,
        
        # Early stopping
        'early_stop': {
            'monitor': 'mAP@0.50',
            'mode': 'max',
            'patience': 15,
            'min_delta': 0.001
        }
    }
    
    print("✅ Training parameters configured")
    return training_params

def main():
    print("🚀 YOLO-NAS Barcode Detection Training - Apple Silicon MPS Optimized")
    print("=" * 80)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Setup MPS environment
    setup_mps_environment()
    
    # Verify MPS setup
    mps_available, device = verify_mps_setup()
    device = torch.device(device)
    
    if not mps_available:
        print("⚠️ Training will proceed on CPU (slower)")
    else:
        print("🍎 Training will use Apple Silicon MPS acceleration")
    
    print()
    
    # Initialize SuperGradients
    print("🔧 Initializing SuperGradients...")
    init_trainer()
    print("✅ SuperGradients initialized")

    # Note: We'll move tensors to MPS manually in the training loop
    # Setting default device causes issues with random number generators
    
    # Create dataloaders
    train_dataloader, val_dataloader = create_mps_optimized_dataloaders(device)
    
    # Load model
    model = load_model_with_transfer_learning(device)
    
    # Create training parameters
    training_params = create_training_params(device)
    
    # Create trainer
    experiment_name = f"yolo_nas_s_barcode_mps_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    trainer = Trainer(
        experiment_name=experiment_name,
        ckpt_root_dir='checkpoints'
    )
    
    print(f"✅ Trainer created: {experiment_name}")
    print()
    
    # Start training
    print("🏃 Starting MPS-accelerated training...")
    print(f"📊 Dataset: 28,696 training + 2,382 validation images")
    print(f"🏗️ Model: YOLO-NAS-S (915 layers transferred from COCO)")
    print(f"🎯 Classes: 2 (Barcode, QR Code)")
    print(f"📱 Device: {device}")
    print(f"⏱️ Epochs: 100")
    print(f"📦 Batch size: 16")
    print()
    
    start_time = time.time()
    
    try:
        trainer.train(
            model=model,
            training_params=training_params,
            train_loader=train_dataloader,
            valid_loader=val_dataloader
        )
        
        end_time = time.time()
        training_duration = end_time - start_time
        
        print()
        print("🎉 Training completed successfully!")
        print(f"⏱️ Total training time: {training_duration/3600:.2f} hours")
        print(f"📊 Results saved in: checkpoints/{experiment_name}/")
        print(f"📈 View logs with: tensorboard --logdir checkpoints/{experiment_name}/")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
