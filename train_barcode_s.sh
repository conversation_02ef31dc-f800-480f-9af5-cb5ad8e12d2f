#!/bin/bash

# YOLO-NAS Barcode Detection Training Script
# Generated on 2025-06-01 15:05:05

echo "🚀 Starting YOLO-NAS Barcode Detection Training"
echo "Model: YOLO-NAS-S"
echo "Dataset: barcodes"
echo "Optimized for Apple Silicon (Mac Mini M4)"
echo ""

# Check if UV is available
if ! command -v uv &> /dev/null; then
    echo "❌ UV package manager not found"
    echo "Please install UV: curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# Check MPS availability
uv run python -c "import torch; print('✅ MPS Available' if torch.backends.mps.is_available() else '❌ MPS Not Available')"

# Start training
uv run python scripts/train_yolo_nas.py \
    --config "configs/custom/barcodes_yolo_nas_s_training.yaml" \
    --log-dir "logs" \
    --experiment-name "barcode_detection_s"

echo ""
echo "🎉 Training completed!"
echo "📊 Check logs in: logs/"
echo "💾 Check checkpoints in: checkpoints/custom/barcodes/"
echo "📈 Monitor with: tensorboard --logdir logs"
