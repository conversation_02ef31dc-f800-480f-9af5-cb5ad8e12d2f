Metadata-Version: 2.4
Name: uritools
Version: 5.0.0
Summary: URI parsing, classification and composition
Home-page: https://github.com/tkem/uritools/
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Other Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
License-File: LICENSE
Dynamic: license-file

uritools
========================================================================

.. image:: https://img.shields.io/pypi/v/uritools
    :target: https://pypi.org/project/uritools
    :alt: Latest PyPI version

.. image:: https://img.shields.io/github/actions/workflow/status/tkem/uritools/ci.yml
   :target: https://github.com/tkem/uritools/actions/workflows/ci.yml
   :alt: CI build status

.. image:: https://img.shields.io/readthedocs/uritools
   :target: https://uritools.readthedocs.io
   :alt: Documentation build status

.. image:: https://img.shields.io/codecov/c/github/tkem/uritools/master.svg
   :target: https://codecov.io/gh/tkem/uritools
   :alt: Test coverage

.. image:: https://img.shields.io/librariesio/sourcerank/pypi/uritools
   :target: https://libraries.io/pypi/uritools
   :alt: Libraries.io SourceRank

.. image:: https://img.shields.io/github/license/tkem/uritools
   :target: https://raw.github.com/tkem/uritools/master/LICENSE
   :alt: License

.. image:: https://img.shields.io/badge/code%20style-black-000000.svg
   :target: https://github.com/psf/black
   :alt: Code style: black


This module provides RFC 3986 compliant functions for parsing,
classifying and composing URIs and URI references, largely replacing
the Python Standard Library's ``urllib.parse`` module.

.. code-block:: pycon

    >>> from uritools import uricompose, urijoin, urisplit, uriunsplit
    >>> uricompose(scheme='foo', host='example.com', port=8042,
    ...            path='/over/there', query={'name': 'ferret'},
    ...            fragment='nose')
    'foo://example.com:8042/over/there?name=ferret#nose'
    >>> parts = urisplit(_)
    >>> parts.scheme
    'foo'
    >>> parts.authority
    'example.com:8042'
    >>> parts.getport(default=80)
    8042
    >>> parts.getquerydict().get('name')
    ['ferret']
    >>> parts.isuri()
    True
    >>> parts.isabsuri()
    False
    >>> urijoin(uriunsplit(parts), '/right/here?name=swallow#beak')
    'foo://example.com:8042/right/here?name=swallow#beak'

For various reasons, ``urllib.parse`` and its Python 2 predecessor
``urlparse`` are not compliant with current Internet standards.  As
stated in `Lib/urllib/parse.py
<https://github.com/python/cpython/blob/3.8/Lib/urllib/parse.py>`_:

    RFC 3986 is considered the current standard and any future changes
    to urlparse module should conform with it.  The urlparse module is
    currently not entirely compliant with this RFC due to defacto
    scenarios for parsing, and for backward compatibility purposes,
    some parsing quirks from older RFCs are retained.

This module aims to provide fully RFC 3986 compliant replacements for
the most commonly used functions found in ``urllib.parse``.  It also
includes functions for distinguishing between the different forms of
URIs and URI references, and for conveniently creating URIs from their
individual components.


Installation
------------------------------------------------------------------------

uritools is available from PyPI_ and can be installed by running::

  pip install uritools


Project Resources
------------------------------------------------------------------------

- `Documentation`_
- `Issue tracker`_
- `Source code`_
- `Change log`_


Related Projects
------------------------------------------------------------------------

- rfc3986_: A Python implementation of RFC 3986 including validation
  and authority parsing.
- rfc3987_: Parsing and validation of URIs (RFC 3896) and IRIs (RFC
  3987).


License
------------------------------------------------------------------------

Copyright (c) 2014-2025 Thomas Kemmer.

Licensed under the `MIT License`_.


.. _PyPI: https://pypi.org/project/uritools/
.. _Documentation: https://uritools.readthedocs.io/
.. _Issue tracker: https://github.com/tkem/uritools/issues/
.. _Source code: https://github.com/tkem/uritools/
.. _Change log: https://github.com/tkem/uritools/blob/master/CHANGELOG.rst
.. _MIT License: https://raw.github.com/tkem/uritools/master/LICENSE

.. _rfc3986: https://pypi.org/project/rfc3986/
.. _rfc3987: https://pypi.org/project/rfc3987/
